import { NextRequest, NextResponse } from 'next/server';
import { rendiService } from '@/services/rendiService';

export async function GET(
  request: NextRequest,
  { params }: { params: { commandId: string } }
) {
  try {
    const { commandId } = params;

    if (!commandId) {
      return NextResponse.json(
        { error: 'Command ID is required' },
        { status: 400 }
      );
    }

    const status = await rendiService.getCommandStatus(commandId);

    return NextResponse.json(status);
  } catch (error) {
    console.error('Status check error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to check command status' },
      { status: 500 }
    );
  }
}
