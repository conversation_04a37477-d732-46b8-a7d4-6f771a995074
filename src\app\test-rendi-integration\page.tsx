'use client';

import { useState } from 'react';
import { rendiService } from '@/services/rendiService';
import { supabaseService } from '@/services/supabaseService';

export default function TestRendiIntegration() {
  const [status, setStatus] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const testRendiConnection = async () => {
    setLoading(true);
    setStatus('Testing rendi.dev connection...');
    
    try {
      // Test with a simple audio file URL
      const testAudioUrl = 'https://storage.rendi.dev/sample/sample.avi'; // Using rendi's sample file
      
      setStatus('Submitting test job...');
      const response = await fetch('/api/rendi/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          audioUrl: testAudioUrl,
          resolution: '1280x720',
          fps: 30,
          duration: 10, // 10 seconds test
          style: 'flowing_waves',
          visualizationParams: { sensitivity: 1.0 },
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to submit job');
      }

      const data = await response.json();
      setStatus(`Job submitted! Command ID: ${data.commandId}`);
      setResult(data);
      
      // Now test status checking
      setTimeout(async () => {
        try {
          setStatus('Checking job status...');
          const statusResponse = await fetch(`/api/rendi/status/${data.commandId}`);
          const statusData = await statusResponse.json();
          setResult(prev => ({ ...prev, status: statusData }));
          setStatus(`Status: ${statusData.status}`);
        } catch (error) {
          setStatus(`Status check failed: ${error}`);
        }
      }, 2000);
      
    } catch (error) {
      setStatus(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      console.error('Test failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const testSupabaseConnection = async () => {
    setLoading(true);
    setStatus('Testing Supabase connection...');
    
    try {
      const hasAccess = await supabaseService.checkBucketAccess();
      if (hasAccess) {
        setStatus('✅ Supabase connection successful!');
        setResult({ supabase: 'Connected' });
      } else {
        setStatus('❌ Supabase connection failed');
        setResult({ supabase: 'Failed' });
      }
    } catch (error) {
      setStatus(`Supabase error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      console.error('Supabase test failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">Rendi.dev Integration Test</h1>
        
        <div className="space-y-6">
          {/* Test Buttons */}
          <div className="flex gap-4">
            <button
              onClick={testRendiConnection}
              disabled={loading}
              className="px-6 py-3 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white rounded-lg font-medium transition-colors"
            >
              {loading ? 'Testing...' : 'Test Rendi.dev API'}
            </button>
            
            <button
              onClick={testSupabaseConnection}
              disabled={loading}
              className="px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white rounded-lg font-medium transition-colors"
            >
              {loading ? 'Testing...' : 'Test Supabase Connection'}
            </button>
          </div>

          {/* Status */}
          {status && (
            <div className="p-4 bg-gray-800 rounded-lg border border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-2">Status</h3>
              <p className="text-gray-300">{status}</p>
            </div>
          )}

          {/* Results */}
          {result && (
            <div className="p-4 bg-gray-800 rounded-lg border border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-2">Results</h3>
              <pre className="text-sm text-gray-300 bg-black/30 p-4 rounded overflow-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}

          {/* Environment Check */}
          <div className="p-4 bg-gray-800 rounded-lg border border-gray-700">
            <h3 className="text-lg font-semibold text-white mb-2">Environment Check</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">Rendi API Key:</span>
                <span className="text-green-400">
                  {process.env.NEXT_PUBLIC_RENDI_API_KEY ? '✅ Set (Public)' : '❌ Not Set (Public)'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Supabase URL:</span>
                <span className="text-green-400">
                  {process.env.NEXT_PUBLIC_SUPABASE_URL ? '✅ Set' : '❌ Not Set'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Supabase Key:</span>
                <span className="text-green-400">
                  {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Not Set'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Supabase Bucket:</span>
                <span className="text-green-400">
                  {process.env.NEXT_PUBLIC_SUPABASE_BUCKET ? '✅ Set' : '❌ Not Set'}
                </span>
              </div>
            </div>
          </div>

          {/* Instructions */}
          <div className="p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-2">Test Instructions</h3>
            <ul className="text-blue-300 space-y-1 text-sm">
              <li>1. Click "Test Rendi.dev API" to verify the API integration works</li>
              <li>2. Click "Test Supabase Connection" to verify file upload capability</li>
              <li>3. Check the results and status messages for any issues</li>
              <li>4. Environment variables should all show ✅ for proper configuration</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
