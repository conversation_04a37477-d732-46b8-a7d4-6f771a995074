export default function StructuredData() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Free Audio Wave Generator",
    "description": "Free online tool to transform your audio files into stunning waveform videos with customizable styles and effects. Create professional music visualizations in minutes - no registration required!",
    "url": "https://www.audiowavegenerator.com",
    "applicationCategory": "MultimediaApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "creator": {
      "@type": "Person",
      "name": "Mattia & Riccardo"
    },
    "featureList": [
      "Free audio to video conversion",
      "Multiple waveform styles",
      "High-quality video export",
      "Real-time audio analysis",
      "Customizable parameters",
      "Professional visualization",
      "No registration required",
      "No watermarks",
      "Unlimited usage"
    ],
    "screenshot": "https://www.audiowavegenerator.com/screenshot.png",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "150"
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}
