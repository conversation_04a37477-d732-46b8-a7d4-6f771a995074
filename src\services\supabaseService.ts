/**
 * Supabase Service for Audio File Management
 * Handles uploading audio files to Supabase storage for rendi.dev processing
 */

import { createClient } from '@supabase/supabase-js';

export interface AudioUploadResult {
  publicUrl: string;
  fileName: string;
  fileSize: number;
  filePath: string;
}

export interface UploadProgress {
  progress: number;
  message: string;
  status: 'uploading' | 'complete' | 'error';
}

class SupabaseService {
  private supabase;
  private readonly bucketName: string;

  constructor() {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    this.bucketName = process.env.NEXT_PUBLIC_SUPABASE_BUCKET || 'temp-video-files';

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Supabase configuration missing. Please check environment variables.');
    }

    this.supabase = createClient(supabaseUrl, supabaseKey);
  }

  /**
   * Upload audio file to Supabase storage
   */
  async uploadAudioFile(
    file: File,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<AudioUploadResult> {
    try {
      // Generate unique filename with timestamp
      const timestamp = Date.now();
      const fileExtension = file.name.split('.').pop() || 'mp3';
      const fileName = `audio_${timestamp}.${fileExtension}`;
      const filePath = `audio/${fileName}`;

      onProgress?.({
        progress: 10,
        message: 'Starting audio upload...',
        status: 'uploading',
      });

      // Validate file type
      if (!this.isValidAudioFile(file)) {
        throw new Error('Invalid audio file type. Please upload MP3, WAV, M4A, or OGG files.');
      }

      // Validate file size (max 100MB as per bucket configuration)
      const maxSize = 100 * 1024 * 1024; // 100MB
      if (file.size > maxSize) {
        throw new Error('File too large. Maximum size is 100MB.');
      }

      onProgress?.({
        progress: 30,
        message: 'Uploading to cloud storage...',
        status: 'uploading',
      });

      // Upload file to Supabase storage
      const { data, error } = await this.supabase.storage
        .from(this.bucketName)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false,
        });

      if (error) {
        console.error('Supabase upload error:', error);
        throw new Error(`Upload failed: ${error.message}`);
      }

      onProgress?.({
        progress: 80,
        message: 'Getting public URL...',
        status: 'uploading',
      });

      // Get public URL
      const { data: urlData } = this.supabase.storage
        .from(this.bucketName)
        .getPublicUrl(filePath);

      if (!urlData.publicUrl) {
        throw new Error('Failed to get public URL for uploaded file');
      }

      onProgress?.({
        progress: 100,
        message: 'Upload complete!',
        status: 'complete',
      });

      return {
        publicUrl: urlData.publicUrl,
        fileName: fileName,
        fileSize: file.size,
        filePath: filePath,
      };
    } catch (error) {
      console.error('Audio upload failed:', error);
      onProgress?.({
        progress: 0,
        message: 'Upload failed',
        status: 'error',
      });
      throw error;
    }
  }

  /**
   * Delete uploaded audio file
   */
  async deleteAudioFile(filePath: string): Promise<void> {
    try {
      const { error } = await this.supabase.storage
        .from(this.bucketName)
        .remove([filePath]);

      if (error) {
        console.error('Failed to delete file:', error);
        // Don't throw error for cleanup operations
      }
    } catch (error) {
      console.error('Error deleting file:', error);
      // Don't throw error for cleanup operations
    }
  }

  /**
   * Validate audio file type
   */
  private isValidAudioFile(file: File): boolean {
    const validTypes = [
      'audio/mpeg',     // MP3
      'audio/mp3',      // MP3 (alternative MIME type)
      'audio/wav',      // WAV
      'audio/wave',     // WAV (alternative MIME type)
      'audio/x-wav',    // WAV (alternative MIME type)
      'audio/mp4',      // M4A
      'audio/m4a',      // M4A
      'audio/ogg',      // OGG
      'audio/webm',     // WebM audio
    ];

    const validExtensions = [
      '.mp3',
      '.wav',
      '.m4a',
      '.ogg',
      '.webm',
    ];

    // Check MIME type
    if (validTypes.includes(file.type)) {
      return true;
    }

    // Check file extension as fallback
    const fileName = file.name.toLowerCase();
    return validExtensions.some(ext => fileName.endsWith(ext));
  }

  /**
   * Get file info from Supabase storage
   */
  async getFileInfo(filePath: string) {
    try {
      const { data, error } = await this.supabase.storage
        .from(this.bucketName)
        .list(filePath.split('/').slice(0, -1).join('/'), {
          search: filePath.split('/').pop(),
        });

      if (error) {
        throw error;
      }

      return data?.[0] || null;
    } catch (error) {
      console.error('Error getting file info:', error);
      return null;
    }
  }

  /**
   * Check if bucket exists and is accessible
   */
  async checkBucketAccess(): Promise<boolean> {
    try {
      const { data, error } = await this.supabase.storage
        .from(this.bucketName)
        .list('', { limit: 1 });

      return !error;
    } catch (error) {
      console.error('Bucket access check failed:', error);
      return false;
    }
  }

  /**
   * Clean up old files (optional utility method)
   */
  async cleanupOldFiles(olderThanHours: number = 24): Promise<void> {
    try {
      const cutoffTime = new Date(Date.now() - olderThanHours * 60 * 60 * 1000);
      
      const { data: files, error } = await this.supabase.storage
        .from(this.bucketName)
        .list('audio/', {
          limit: 100,
          sortBy: { column: 'created_at', order: 'asc' },
        });

      if (error || !files) {
        console.error('Failed to list files for cleanup:', error);
        return;
      }

      const filesToDelete = files
        .filter(file => new Date(file.created_at) < cutoffTime)
        .map(file => `audio/${file.name}`);

      if (filesToDelete.length > 0) {
        const { error: deleteError } = await this.supabase.storage
          .from(this.bucketName)
          .remove(filesToDelete);

        if (deleteError) {
          console.error('Failed to delete old files:', deleteError);
        } else {
          console.log(`Cleaned up ${filesToDelete.length} old files`);
        }
      }
    } catch (error) {
      console.error('Cleanup operation failed:', error);
    }
  }
}

export const supabaseService = new SupabaseService();
