import { NextRequest, NextResponse } from 'next/server';
import { rendiService } from '@/services/rendiService';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { audioUrl, resolution, fps, duration, style, visualizationParams } = body;

    // Validate required fields
    if (!audioUrl || !resolution || !fps || !duration || !style) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create FFmpeg command for video generation
    const [width, height] = resolution.split('x').map(Number);
    
    // Create a more sophisticated waveform visualization command
    const ffmpegCommand = createAdvancedWaveformCommand(
      width,
      height,
      fps,
      duration,
      style,
      visualizationParams?.sensitivity || 1.0
    );

    const request_data = {
      ffmpeg_command: ffmpegCommand,
      input_files: {
        in_1: audioUrl,
      },
      output_files: {
        out_1: 'visualization.mp4',
      },
      max_command_run_seconds: 600, // 10 minutes
      vcpu_count: 8,
    };

    const response = await rendiService.submitCommand(request_data);

    return NextResponse.json({ 
      commandId: response.command_id,
      message: 'Video generation job submitted successfully'
    });
  } catch (error) {
    console.error('Video generation submission error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to submit video generation job' },
      { status: 500 }
    );
  }
}

function createAdvancedWaveformCommand(
  width: number,
  height: number,
  fps: number,
  duration: number,
  style: string,
  sensitivity: number
): string {
  const waveformHeight = Math.floor(height * 0.4 * sensitivity);
  const waveformY = Math.floor((height - waveformHeight) / 2);

  // Optimize encoding settings for faster processing
  const encodingSettings = `-c:v libx264 -preset fast -crf 20 -c:a aac -b:a 128k -pix_fmt yuv420p -movflags +faststart`;

  switch (style) {
    case 'flowing_waves':
      return `-i {{in_1}} -filter_complex "[0:a]showwaves=s=${width}x${waveformHeight}:mode=cline:colors=0x00ff88|0x0088ff|0xff8800:rate=${fps}:split_channels=1[waves];color=black:s=${width}x${height}:rate=${fps}:duration=${duration}[bg];[bg][waves]overlay=0:${waveformY}[v]" -map "[v]" -map 0:a ${encodingSettings} {{out_1}}`;

    case 'neon_effects':
      return `-i {{in_1}} -filter_complex "[0:a]showwaves=s=${width}x${waveformHeight}:mode=cline:colors=0x00ffff|0xff00ff|0xffff00:rate=${fps}:split_channels=1[waves];color=black:s=${width}x${height}:rate=${fps}:duration=${duration}[bg];[bg][waves]overlay=0:${waveformY}[v]" -map "[v]" -map 0:a ${encodingSettings} {{out_1}}`;

    case 'bar_equalizer':
      return `-i {{in_1}} -filter_complex "[0:a]showfreqs=s=${width}x${waveformHeight}:mode=bar:colors=0x00ff00|0xffff00|0xff0000:rate=${fps}:fscale=log[freq];color=black:s=${width}x${height}:rate=${fps}:duration=${duration}[bg];[bg][freq]overlay=0:${waveformY}[v]" -map "[v]" -map 0:a ${encodingSettings} {{out_1}}`;

    case 'circular_spectrum':
      return `-i {{in_1}} -filter_complex "[0:a]showfreqs=s=${width}x${waveformHeight}:mode=line:colors=0xff6600|0x00ff66|0x6600ff:rate=${fps}:fscale=log[freq];color=black:s=${width}x${height}:rate=${fps}:duration=${duration}[bg];[bg][freq]overlay=0:${waveformY}[v]" -map "[v]" -map 0:a ${encodingSettings} {{out_1}}`;

    case 'minimal_design':
      return `-i {{in_1}} -filter_complex "[0:a]showwaves=s=${width}x${waveformHeight}:mode=line:colors=0xffffff:rate=${fps}:split_channels=1[waves];color=black:s=${width}x${height}:rate=${fps}:duration=${duration}[bg];[bg][waves]overlay=0:${waveformY}[v]" -map "[v]" -map 0:a ${encodingSettings} {{out_1}}`;

    case 'layered_waves':
      return `-i {{in_1}} -filter_complex "[0:a]showwaves=s=${width}x${waveformHeight}:mode=cline:colors=0x4444ff|0x44ff44|0xff4444:rate=${fps}:split_channels=1[waves];color=black:s=${width}x${height}:rate=${fps}:duration=${duration}[bg];[bg][waves]overlay=0:${waveformY}[v]" -map "[v]" -map 0:a ${encodingSettings} {{out_1}}`;

    case 'energy_burst':
      return `-i {{in_1}} -filter_complex "[0:a]showfreqs=s=${width}x${waveformHeight}:mode=bar:colors=0xff0000|0xff8800|0xffff00|0x88ff00|0x00ff00:rate=${fps}:fscale=log[freq];color=black:s=${width}x${height}:rate=${fps}:duration=${duration}[bg];[bg][freq]overlay=0:${waveformY}[v]" -map "[v]" -map 0:a ${encodingSettings} {{out_1}}`;

    case 'geometric_patterns':
      return `-i {{in_1}} -filter_complex "[0:a]showfreqs=s=${width}x${waveformHeight}:mode=line:colors=0x00aaff|0xaa00ff|0xffaa00:rate=${fps}:fscale=lin[freq];color=black:s=${width}x${height}:rate=${fps}:duration=${duration}[bg];[bg][freq]overlay=0:${waveformY}[v]" -map "[v]" -map 0:a ${encodingSettings} {{out_1}}`;

    case 'particle_system':
      return `-i {{in_1}} -filter_complex "[0:a]showwaves=s=${width}x${waveformHeight}:mode=point:colors=0x00ffaa|0xaa00ff|0xffaa00:rate=${fps}:split_channels=1[waves];color=black:s=${width}x${height}:rate=${fps}:duration=${duration}[bg];[bg][waves]overlay=0:${waveformY}[v]" -map "[v]" -map 0:a ${encodingSettings} {{out_1}}`;

    case '3d_visualization':
      return `-i {{in_1}} -filter_complex "[0:a]showfreqs=s=${width}x${waveformHeight}:mode=bar:colors=0x0088ff|0x8800ff|0xff8800:rate=${fps}:fscale=log[freq];color=black:s=${width}x${height}:rate=${fps}:duration=${duration}[bg];[bg][freq]overlay=0:${waveformY}[v]" -map "[v]" -map 0:a ${encodingSettings} {{out_1}}`;

    default:
      // Default flowing waves
      return `-i {{in_1}} -filter_complex "[0:a]showwaves=s=${width}x${waveformHeight}:mode=cline:colors=0x00ff88:rate=${fps}:split_channels=1[waves];color=black:s=${width}x${height}:rate=${fps}:duration=${duration}[bg];[bg][waves]overlay=0:${waveformY}[v]" -map "[v]" -map 0:a ${encodingSettings} {{out_1}}`;
  }
}
