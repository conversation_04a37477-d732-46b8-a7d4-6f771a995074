import { NextRequest, NextResponse } from 'next/server';
import { rendiService } from '@/services/rendiService';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { audioUrl, resolution, fps, duration, style, visualizationParams } = body;

    // Validate required fields
    if (!audioUrl || !resolution || !fps || !duration || !style) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create FFmpeg command for video generation
    const [width, height] = resolution.split('x').map(Number);
    
    // Create a more sophisticated waveform visualization command
    const ffmpegCommand = createAdvancedWaveformCommand(
      width,
      height,
      fps,
      duration,
      style,
      visualizationParams?.sensitivity || 1.0
    );

    const request_data = {
      ffmpeg_command: ffmpegCommand,
      input_files: {
        in_1: audioUrl,
      },
      output_files: {
        out_1: 'visualization.mp4',
      },
      max_command_run_seconds: 600, // 10 minutes
      vcpu_count: 8,
    };

    const response = await rendiService.submitCommand(request_data);

    return NextResponse.json({ 
      commandId: response.command_id,
      message: 'Video generation job submitted successfully'
    });
  } catch (error) {
    console.error('Video generation submission error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to submit video generation job' },
      { status: 500 }
    );
  }
}

function createAdvancedWaveformCommand(
  width: number,
  height: number,
  fps: number,
  duration: number,
  style: string,
  sensitivity: number
): string {
  const waveformHeight = Math.floor(height * 0.4 * sensitivity);
  const waveformY = Math.floor((height - waveformHeight) / 2);

  // Optimize encoding settings for faster processing
  const encodingSettings = `-c:v libx264 -preset fast -crf 20 -c:a aac -b:a 128k -pix_fmt yuv420p -movflags +faststart`;

  switch (style) {
    case 'flowing_waves':
      return createFlowingWavesCommand(width, height, fps, duration, encodingSettings);

    case 'neon_effects':
      return createNeonEffectsCommand(width, height, fps, duration, encodingSettings);

    case 'bar_equalizer':
      return `-i {{in_1}} -filter_complex "[0:a]showfreqs=s=${width}x${waveformHeight}:mode=bar:colors=0x00ff00|0xffff00|0xff0000:rate=${fps}:fscale=log[freq];color=black:s=${width}x${height}:rate=${fps}:duration=${duration}[bg];[bg][freq]overlay=0:${waveformY}[v]" -map "[v]" -map 0:a ${encodingSettings} {{out_1}}`;

    case 'circular_spectrum':
      return createCircularSpectrumCommand(width, height, fps, duration, encodingSettings);

    case 'minimal_design':
      return `-i {{in_1}} -filter_complex "[0:a]showwaves=s=${width}x${waveformHeight}:mode=line:colors=0xffffff:rate=${fps}:split_channels=1[waves];color=black:s=${width}x${height}:rate=${fps}:duration=${duration}[bg];[bg][waves]overlay=0:${waveformY}[v]" -map "[v]" -map 0:a ${encodingSettings} {{out_1}}`;

    case 'layered_waves':
      return `-i {{in_1}} -filter_complex "[0:a]showwaves=s=${width}x${waveformHeight}:mode=cline:colors=0x4444ff|0x44ff44|0xff4444:rate=${fps}:split_channels=1[waves];color=black:s=${width}x${height}:rate=${fps}:duration=${duration}[bg];[bg][waves]overlay=0:${waveformY}[v]" -map "[v]" -map 0:a ${encodingSettings} {{out_1}}`;

    case 'energy_burst':
      return `-i {{in_1}} -filter_complex "[0:a]showfreqs=s=${width}x${waveformHeight}:mode=bar:colors=0xff0000|0xff8800|0xffff00|0x88ff00|0x00ff00:rate=${fps}:fscale=log[freq];color=black:s=${width}x${height}:rate=${fps}:duration=${duration}[bg];[bg][freq]overlay=0:${waveformY}[v]" -map "[v]" -map 0:a ${encodingSettings} {{out_1}}`;

    case 'geometric_patterns':
      return `-i {{in_1}} -filter_complex "[0:a]showfreqs=s=${width}x${waveformHeight}:mode=line:colors=0x00aaff|0xaa00ff|0xffaa00:rate=${fps}:fscale=lin[freq];color=black:s=${width}x${height}:rate=${fps}:duration=${duration}[bg];[bg][freq]overlay=0:${waveformY}[v]" -map "[v]" -map 0:a ${encodingSettings} {{out_1}}`;

    case 'particle_system':
      return `-i {{in_1}} -filter_complex "[0:a]showwaves=s=${width}x${waveformHeight}:mode=point:colors=0x00ffaa|0xaa00ff|0xffaa00:rate=${fps}:split_channels=1[waves];color=black:s=${width}x${height}:rate=${fps}:duration=${duration}[bg];[bg][waves]overlay=0:${waveformY}[v]" -map "[v]" -map 0:a ${encodingSettings} {{out_1}}`;

    case '3d_visualization':
      return `-i {{in_1}} -filter_complex "[0:a]showfreqs=s=${width}x${waveformHeight}:mode=bar:colors=0x0088ff|0x8800ff|0xff8800:rate=${fps}:fscale=log[freq];color=black:s=${width}x${height}:rate=${fps}:duration=${duration}[bg];[bg][freq]overlay=0:${waveformY}[v]" -map "[v]" -map 0:a ${encodingSettings} {{out_1}}`;

    default:
      // Default flowing waves
      return `-i {{in_1}} -filter_complex "[0:a]showwaves=s=${width}x${waveformHeight}:mode=cline:colors=0x00ff88:rate=${fps}:split_channels=1[waves];color=black:s=${width}x${height}:rate=${fps}:duration=${duration}[bg];[bg][waves]overlay=0:${waveformY}[v]" -map "[v]" -map 0:a ${encodingSettings} {{out_1}}`;
  }
}

function createCircularSpectrumCommand(
  width: number,
  height: number,
  fps: number,
  duration: number,
  encodingSettings: string
): string {
  const centerX = width / 2;
  const centerY = height / 2;
  const radius = Math.min(width, height) * 0.3;

  // Simplified but effective circular spectrum using radial bars
  return `-i {{in_1}} -filter_complex "
    [0:a]showfreqs=s=360x100:mode=bar:colors=0xff0000|0xff4400|0xff8800|0xffcc00|0xffff00|0xccff00|0x88ff00|0x44ff00|0x00ff00|0x00ff44|0x00ff88|0x00ffcc|0x00ffff|0x00ccff|0x0088ff|0x0044ff|0x0000ff|0x4400ff|0x8800ff|0xcc00ff|0xff00ff|0xff00cc|0xff0088|0xff0044:rate=${fps}:fscale=log[freq_bars];
    color=black:s=${width}x${height}:rate=${fps}:duration=${duration}[bg];
    [freq_bars]scale=${radius*2}x${radius*2}:flags=bicubic[freq_scaled];
    [freq_scaled]format=rgba,geq='
      r=r(X,Y)*sin(T*3+X*0.1)*0.7+0.3:
      g=g(X,Y)*sin(T*3.5+X*0.1)*0.7+0.3:
      b=b(X,Y)*sin(T*4+X*0.1)*0.7+0.3:
      a=if(hypot(X-${radius},Y-${radius})<${radius*0.9},if(hypot(X-${radius},Y-${radius})>${radius*0.4},255,0),0)
    '[freq_circular];
    [bg][freq_circular]overlay=${centerX-radius}:${centerY-radius}:format=auto,format=yuv420p[v]
  " -map "[v]" -map 0:a ${encodingSettings} {{out_1}}`;
}

function createFlowingWavesCommand(
  width: number,
  height: number,
  fps: number,
  duration: number,
  encodingSettings: string
): string {
  const centerY = height / 2;

  // Advanced flowing waves with multiple layers and smooth animation
  return `-i {{in_1}} -filter_complex "
    [0:a]showwaves=s=${width}x${Math.floor(height*0.3)}:mode=cline:colors=0x00ff88:rate=${fps}:split_channels=1[wave1];
    [0:a]showwaves=s=${width}x${Math.floor(height*0.25)}:mode=cline:colors=0x0088ff:rate=${fps}:split_channels=1[wave2];
    [0:a]showwaves=s=${width}x${Math.floor(height*0.2)}:mode=cline:colors=0xff8800:rate=${fps}:split_channels=1[wave3];
    color=black:s=${width}x${height}:rate=${fps}:duration=${duration}[bg];
    [wave1]geq='r=r(X,Y)*sin(T*2+X*0.01)*0.8+0.2:g=g(X,Y)*sin(T*2.5+X*0.01)*0.8+0.2:b=b(X,Y)*sin(T*3+X*0.01)*0.8+0.2'[wave1_anim];
    [wave2]geq='r=r(X,Y)*sin(T*1.8+X*0.008)*0.7+0.3:g=g(X,Y)*sin(T*2.3+X*0.008)*0.7+0.3:b=b(X,Y)*sin(T*2.8+X*0.008)*0.7+0.3'[wave2_anim];
    [wave3]geq='r=r(X,Y)*sin(T*2.2+X*0.012)*0.6+0.4:g=g(X,Y)*sin(T*2.7+X*0.012)*0.6+0.4:b=b(X,Y)*sin(T*3.2+X*0.012)*0.6+0.4'[wave3_anim];
    [bg][wave1_anim]overlay=0:${centerY-Math.floor(height*0.15)}:format=auto,format=yuv420p[tmp1];
    [tmp1][wave2_anim]overlay=0:${centerY-Math.floor(height*0.125)}:format=auto,format=yuv420p[tmp2];
    [tmp2][wave3_anim]overlay=0:${centerY-Math.floor(height*0.1)}:format=auto,format=yuv420p[v]
  " -map "[v]" -map 0:a ${encodingSettings} {{out_1}}`;
}

function createNeonEffectsCommand(
  width: number,
  height: number,
  fps: number,
  duration: number,
  encodingSettings: string
): string {
  const centerY = height / 2;
  const waveHeight = Math.floor(height * 0.4);

  // Neon effects with glow and pulsing animation
  return `-i {{in_1}} -filter_complex "
    [0:a]showwaves=s=${width}x${waveHeight}:mode=cline:colors=0x00ffff:rate=${fps}:split_channels=1[wave_base];
    [wave_base]geq='
      r=if(r(X,Y)>0.1, r(X,Y)*sin(T*4)*0.5+0.5, 0):
      g=if(g(X,Y)>0.1, g(X,Y)*sin(T*4+1)*0.5+0.5, 0):
      b=if(b(X,Y)>0.1, b(X,Y)*sin(T*4+2)*0.5+0.5, 0)
    '[wave_pulse];
    [wave_pulse]boxblur=5:1[wave_glow];
    color=black:s=${width}x${height}:rate=${fps}:duration=${duration}[bg];
    [bg][wave_glow]overlay=0:${centerY-waveHeight/2}:format=auto,format=yuv420p[tmp];
    [tmp][wave_pulse]overlay=0:${centerY-waveHeight/2}:format=auto,format=yuv420p[v]
  " -map "[v]" -map 0:a ${encodingSettings} {{out_1}}`;
}
