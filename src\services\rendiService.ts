/**
 * Rendi.dev API Service
 * Handles video generation using rendi.dev's FFmpeg-as-a-Service
 */

export interface RendiCommandRequest {
  ffmpeg_command: string;
  input_files: Record<string, string>;
  output_files: Record<string, string>;
  max_command_run_seconds?: number;
  vcpu_count?: number;
}

export interface RendiCommandResponse {
  command_id: string;
}

export interface RendiStatusResponse {
  command_id: string;
  status: 'QUEUED' | 'PROCESSING' | 'SUCCESS' | 'FAILED';
  command_type: string;
  ffmpeg_command_run_seconds?: number;
  total_processing_seconds?: number;
  vcpu_count: number;
  original_request: RendiCommandRequest;
  output_files?: Record<string, {
    file_id: string;
    size_mbytes: number;
    file_type: string;
    file_format: string;
    storage_url: string;
    width?: number;
    height?: number;
    duration?: number;
    frame_rate?: number;
  }>;
  error_message?: string;
}

export interface VideoGenerationOptions {
  audioUrl: string;
  resolution: '1280x720' | '1920x1080' | '2560x1440' | '3840x2160';
  fps: 30 | 60;
  duration: number;
  style: string;
  visualizationParams: {
    sensitivity: number;
  };
}

export interface VideoGenerationProgress {
  status: 'uploading' | 'queued' | 'processing' | 'complete' | 'error';
  progress: number;
  message: string;
  commandId?: string;
  videoUrl?: string;
  error?: string;
}

class RendiService {
  private readonly isServer: boolean;
  private readonly apiKey: string;
  private readonly baseUrl = 'https://api.rendi.dev/v1';

  constructor() {
    this.isServer = typeof window === 'undefined';
    this.apiKey = process.env.RENDI_API_KEY || '';

    // Only require API key on server side
    if (this.isServer && !this.apiKey) {
      throw new Error('RENDI_API_KEY environment variable is required');
    }
  }

  /**
   * Submit an FFmpeg command to rendi.dev (server-side only)
   */
  async submitCommand(request: RendiCommandRequest): Promise<RendiCommandResponse> {
    if (!this.isServer) {
      throw new Error('submitCommand can only be called on the server side');
    }

    try {
      const response = await fetch(`${this.baseUrl}/run-ffmpeg-command`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-KEY': this.apiKey,
        },
        body: JSON.stringify({
          ...request,
          max_command_run_seconds: request.max_command_run_seconds || 600, // 10 minutes default
          vcpu_count: request.vcpu_count || 8, // Use 8 vCPUs for faster processing
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Rendi API error (${response.status}): ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to submit command to rendi.dev:', error);
      throw new Error(`Failed to submit video generation command: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Poll command status (server-side only)
   */
  async getCommandStatus(commandId: string): Promise<RendiStatusResponse> {
    if (!this.isServer) {
      throw new Error('getCommandStatus can only be called on the server side');
    }

    try {
      const response = await fetch(`${this.baseUrl}/commands/${commandId}`, {
        method: 'GET',
        headers: {
          'X-API-KEY': this.apiKey,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Rendi API error (${response.status}): ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to get command status from rendi.dev:', error);
      throw new Error(`Failed to get command status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Submit video generation job via API route (client-side)
   */
  async submitVideoGeneration(options: VideoGenerationOptions): Promise<{ commandId: string }> {
    if (this.isServer) {
      throw new Error('submitVideoGeneration should be called from the client side');
    }

    try {
      const response = await fetch('/api/rendi/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(options),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to submit video generation job');
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to submit video generation:', error);
      throw new Error(`Failed to submit video generation: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check command status via API route (client-side)
   */
  async checkCommandStatus(commandId: string): Promise<RendiStatusResponse> {
    if (this.isServer) {
      throw new Error('checkCommandStatus should be called from the client side');
    }

    try {
      const response = await fetch(`/api/rendi/status/${commandId}`, {
        method: 'GET',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to check command status');
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to check command status:', error);
      throw new Error(`Failed to check command status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Poll command until completion with progress updates (client-side)
   */
  async pollCommandWithProgress(
    commandId: string,
    onProgress: (progress: VideoGenerationProgress) => void
  ): Promise<RendiStatusResponse> {
    const startTime = Date.now();
    const maxWaitTime = 10 * 60 * 1000; // 10 minutes max
    let pollInterval = 2000; // Start with 2 seconds

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const status = await this.checkCommandStatus(commandId);

        // Calculate progress based on status and elapsed time
        let progress = 0;
        let message = '';

        switch (status.status) {
          case 'QUEUED':
            progress = 10;
            message = 'Video generation queued...';
            break;
          case 'PROCESSING':
            // Estimate progress based on elapsed time (rough estimate)
            const elapsed = Date.now() - startTime;
            const estimatedTotal = 180000; // 3 minutes estimated
            progress = Math.min(90, 20 + (elapsed / estimatedTotal) * 70);
            message = 'Generating video frames and encoding...';
            break;
          case 'SUCCESS':
            progress = 100;
            message = 'Video generation complete!';
            onProgress({
              status: 'complete',
              progress,
              message,
              commandId,
              videoUrl: status.output_files?.out_1?.storage_url,
            });
            return status;
          case 'FAILED':
            throw new Error(status.error_message || 'Video generation failed');
        }

        onProgress({
          status: status.status === 'QUEUED' ? 'queued' : 'processing',
          progress,
          message,
          commandId,
        });

        // Increase poll interval gradually to reduce API calls
        if (status.status === 'PROCESSING') {
          pollInterval = Math.min(pollInterval * 1.1, 5000); // Max 5 seconds
        }

        await new Promise(resolve => setTimeout(resolve, pollInterval));
      } catch (error) {
        console.error('Error polling command status:', error);
        onProgress({
          status: 'error',
          progress: 0,
          message: 'Failed to check video generation status',
          error: error instanceof Error ? error.message : 'Unknown error',
        });
        throw error;
      }
    }

    throw new Error('Video generation timed out after 10 minutes');
  }

  /**
   * Generate a video using rendi.dev (client-side method)
   * This is the main method that orchestrates the entire process
   */
  async generateVideo(
    options: VideoGenerationOptions,
    onProgress: (progress: VideoGenerationProgress) => void
  ): Promise<string> {
    try {
      onProgress({
        status: 'uploading',
        progress: 5,
        message: 'Preparing video generation...',
      });

      onProgress({
        status: 'queued',
        progress: 10,
        message: 'Submitting video generation job...',
      });

      // Submit job via API route
      const { commandId } = await this.submitVideoGeneration(options);

      onProgress({
        status: 'queued',
        progress: 15,
        message: 'Video generation job submitted, waiting for processing...',
        commandId,
      });

      // Poll for completion
      const finalStatus = await this.pollCommandWithProgress(commandId, onProgress);

      if (finalStatus.output_files?.out_1?.storage_url) {
        return finalStatus.output_files.out_1.storage_url;
      } else {
        throw new Error('No output video URL received from rendi.dev');
      }
    } catch (error) {
      console.error('Video generation failed:', error);
      onProgress({
        status: 'error',
        progress: 0,
        message: 'Video generation failed',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Create FFmpeg command for waveform visualization
   * This creates a basic audio waveform visualization
   */
  private createWaveformFFmpegCommand(
    width: number,
    height: number,
    fps: number,
    duration: number,
    style: string
  ): string {
    // Basic waveform visualization using FFmpeg's showwaves filter
    // This is a starting point - we'll enhance this with more sophisticated visualizations
    
    const waveformHeight = Math.floor(height * 0.3); // 30% of video height
    const waveformY = Math.floor((height - waveformHeight) / 2); // Center vertically
    
    return `-i {{in_1}} -filter_complex "[0:a]showwaves=s=${width}x${waveformHeight}:mode=cline:colors=0x00ff00:rate=${fps}[waves];color=black:s=${width}x${height}:rate=${fps}:duration=${duration}[bg];[bg][waves]overlay=0:${waveformY}[v]" -map "[v]" -map 0:a -c:v libx264 -c:a aac -pix_fmt yuv420p -preset medium -crf 23 {{out_1}}`;
  }
}

export const rendiService = new RendiService();
