'use client';

import { useCallback, useState, useRef } from 'react';
import { Upload, Music, AlertCircle, CheckCircle } from 'lucide-react';
import { AudioUploaderProps, AudioData, FileValidation } from '@/types';

const SUPPORTED_FORMATS = ['audio/mpeg', 'audio/wav', 'audio/flac', 'audio/mp4', 'audio/ogg'];
const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
const MAX_DURATION = 900; // 15 minutes

export default function AudioUploader({ onAudioLoaded, disabled = false }: AudioUploaderProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [statusMessage, setStatusMessage] = useState('');
  const [progress, setProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): FileValidation => {
    if (!SUPPORTED_FORMATS.includes(file.type)) {
      return {
        isValid: false,
        error: `Unsupported format. Please use MP3, WAV, FLAC, M4A, or OGG files.`
      };
    }

    if (file.size > MAX_FILE_SIZE) {
      return {
        isValid: false,
        error: `File too large. Maximum size is ${MAX_FILE_SIZE / (1024 * 1024)}MB.`
      };
    }

    return {
      isValid: true,
      fileType: file.type,
      size: file.size
    };
  };

  const analyzeAudio = async (audioBuffer: AudioBuffer): Promise<{ frequencyData: Float32Array[], amplitudeData: Float32Array[] }> => {
    const sampleRate = audioBuffer.sampleRate;
    const frameSize = 2048; // Larger frame size for better frequency resolution
    const hopSize = 1024; // Larger hop size for efficiency
    const numFrames = Math.floor((audioBuffer.length - frameSize) / hopSize) + 1;

    const frequencyData: Float32Array[] = [];
    const amplitudeData: Float32Array[] = [];

    // Get channel data
    const channelData = audioBuffer.getChannelData(0);

    // Create a simple FFT-like analysis using Web Audio API offline context
    const offlineContext = new OfflineAudioContext(1, audioBuffer.length, sampleRate);
    const source = offlineContext.createBufferSource();
    const analyser = offlineContext.createAnalyser();

    analyser.fftSize = 256; // This gives us 128 frequency bins
    analyser.smoothingTimeConstant = 0.3;

    source.buffer = audioBuffer;
    source.connect(analyser);
    analyser.connect(offlineContext.destination);

    // Process audio in chunks to avoid blocking
    for (let i = 0; i < numFrames; i++) {
      const progress = (i / numFrames) * 100;
      setProgress(40 + (progress * 0.4)); // 40-80% of total progress
      setStatusMessage(`Analyzing audio... ${Math.round(progress)}%`);

      const startSample = i * hopSize;
      const endSample = Math.min(startSample + frameSize, channelData.length);

      // Create frequency bins (128 bins for visualization)
      const frequencyBins = 128;
      const frequencyArray = new Float32Array(frequencyBins);
      const amplitudeArray = new Float32Array(frequencyBins);

      // Calculate RMS amplitude for this frame
      let rms = 0;
      for (let j = startSample; j < endSample; j++) {
        rms += channelData[j] * channelData[j];
      }
      rms = Math.sqrt(rms / (endSample - startSample));

      // Simulate frequency analysis using time-domain data
      // This is a simplified approach that creates frequency-like data
      for (let k = 0; k < frequencyBins; k++) {
        const normalizedFreq = k / frequencyBins;

        // Calculate energy in different frequency ranges
        let binEnergy = 0;
        const windowSize = Math.max(1, Math.floor((endSample - startSample) / frequencyBins));
        const binStart = startSample + (k * windowSize);
        const binEnd = Math.min(binStart + windowSize, endSample);

        // Apply a simple frequency-domain-like transformation
        for (let j = binStart; j < binEnd; j++) {
          const sample = channelData[j];
          // Simulate frequency content by applying different weights
          const freqWeight = Math.cos(normalizedFreq * Math.PI * 2 * (j - binStart) / windowSize);
          binEnergy += sample * sample * Math.abs(freqWeight);
        }

        binEnergy = Math.sqrt(binEnergy / (binEnd - binStart));

        // Apply realistic frequency response curve
        let freqWeight = 1.0;
        if (normalizedFreq < 0.05) {
          // Sub-bass - reduced
          freqWeight = 0.7;
        } else if (normalizedFreq < 0.15) {
          // Bass frequencies - boosted
          freqWeight = 1.5;
        } else if (normalizedFreq < 0.35) {
          // Low-mid frequencies - slightly boosted
          freqWeight = 1.2;
        } else if (normalizedFreq < 0.65) {
          // Mid frequencies - neutral
          freqWeight = 1.0;
        } else if (normalizedFreq < 0.85) {
          // High-mid frequencies - slightly reduced
          freqWeight = 0.8;
        } else {
          // High frequencies - more reduced
          freqWeight = 0.6;
        }

        // Apply energy scaling and ensure some energy for visualization
        const scaledEnergy = binEnergy * freqWeight * 3.0; // Increase overall energy
        frequencyArray[k] = Math.max(0.02, scaledEnergy); // Ensure minimum energy
        amplitudeArray[k] = rms;
      }

      frequencyData.push(frequencyArray);
      amplitudeData.push(amplitudeArray);

      // Yield control to prevent blocking
      if (i % 25 === 0) {
        await new Promise(resolve => setTimeout(resolve, 1));
      }
    }

    return { frequencyData, amplitudeData };
  };

  const processAudioFile = useCallback(async (file: File) => {
    setIsProcessing(true);
    setUploadStatus('idle');
    setProgress(0);
    setStatusMessage('Reading file...');

    try {
      // Read file as array buffer
      const arrayBuffer = await file.arrayBuffer();
      setProgress(20);
      setStatusMessage('Decoding audio...');

      // Create audio context and decode
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

      // Validate duration
      if (audioBuffer.duration > MAX_DURATION) {
        throw new Error(`Audio too long. Maximum duration is ${MAX_DURATION / 60} minutes.`);
      }

      setProgress(40);
      setStatusMessage('Analyzing audio data...');

      // Analyze audio for frequency and amplitude data
      const { frequencyData, amplitudeData } = await analyzeAudio(audioBuffer);

      setProgress(90);
      setStatusMessage('Finalizing...');

      const audioData: AudioData = {
        audioBuffer,
        frequencyData,
        amplitudeData,
        metadata: {
          duration: audioBuffer.duration,
          sampleRate: audioBuffer.sampleRate,
          numberOfChannels: audioBuffer.numberOfChannels,
          fileName: file.name,
          fileSize: file.size
        }
      };

      setProgress(100);
      setStatusMessage('Audio loaded successfully!');
      setUploadStatus('success');

      console.log('Audio data loaded:', {
        duration: audioData.metadata.duration,
        sampleRate: audioData.metadata.sampleRate,
        frequencyFrames: audioData.frequencyData.length,
        firstFrameLength: audioData.frequencyData[0]?.length
      });

      onAudioLoaded(audioData);

    } catch (error) {
      console.error('Error processing audio:', error);
      setUploadStatus('error');
      setStatusMessage(error instanceof Error ? error.message : 'Failed to process audio file');
    } finally {
      setIsProcessing(false);
    }
  }, [onAudioLoaded]);

  const handleFileSelect = useCallback(async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const file = files[0];
    const validation = validateFile(file);

    if (!validation.isValid) {
      setUploadStatus('error');
      setStatusMessage(validation.error || 'Invalid file');
      return;
    }

    await processAudioFile(file);
  }, [processAudioFile]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    if (disabled || isProcessing) return;
    handleFileSelect(e.dataTransfer.files);
  }, [disabled, isProcessing, handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled && !isProcessing) {
      setIsDragOver(true);
    }
  }, [disabled, isProcessing]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleClick = () => {
    if (disabled || isProcessing) return;
    fileInputRef.current?.click();
  };

  const getStatusIcon = () => {
    if (isProcessing) return <Upload className="w-8 h-8 animate-pulse" />;
    if (uploadStatus === 'success') return <CheckCircle className="w-8 h-8 text-green-400" />;
    if (uploadStatus === 'error') return <AlertCircle className="w-8 h-8 text-red-400" />;
    return <Music className="w-8 h-8" />;
  };

  const getStatusColor = () => {
    if (uploadStatus === 'success') return 'text-green-400';
    if (uploadStatus === 'error') return 'text-red-400';
    return 'text-gray-300';
  };

  return (
    <div className="space-y-4">
      <div
        className={`
          relative border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all duration-200
          ${isDragOver ? 'border-purple-400 bg-purple-400/10' : 'border-gray-600 hover:border-gray-500'}
          ${disabled || isProcessing ? 'opacity-50 cursor-not-allowed' : ''}
          ${uploadStatus === 'success' ? 'border-green-400 bg-green-400/10' : ''}
          ${uploadStatus === 'error' ? 'border-red-400 bg-red-400/10' : ''}
        `}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={handleClick}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="audio/*"
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
          disabled={disabled || isProcessing}
        />
        
        <div className="flex flex-col items-center space-y-4">
          {getStatusIcon()}
          
          <div>
            <p className={`text-lg font-medium ${getStatusColor()}`}>
              {isProcessing ? 'Processing...' : 
               uploadStatus === 'success' ? 'Audio loaded!' :
               uploadStatus === 'error' ? 'Upload failed' :
               'Drop your audio file here'}
            </p>
            <p className="text-sm text-gray-400 mt-1">
              {isProcessing ? statusMessage :
               uploadStatus === 'error' ? statusMessage :
               'Supports MP3, WAV, FLAC, M4A, OGG (max 100MB, 15min)'}
            </p>
          </div>
        </div>

        {isProcessing && (
          <div className="mt-4">
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div 
                className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
