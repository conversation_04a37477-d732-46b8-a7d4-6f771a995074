import { NextRequest, NextResponse } from 'next/server';
import { rendiService } from '@/services/rendiService';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { audioUrl, resolution, fps, duration, style, visualizationParams } = body;

    // Validate required fields
    if (!audioUrl || !resolution || !fps || !duration || !style) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Generate video using rendi.dev
    const videoUrl = await rendiService.generateVideo(
      {
        audioUrl,
        resolution,
        fps,
        duration,
        style,
        visualizationParams: visualizationParams || { sensitivity: 1.0 },
      },
      // Progress callback - we'll handle this differently for API routes
      (progress) => {
        // For now, we'll just log progress
        // In a real implementation, you might want to use WebSockets or Server-Sent Events
        console.log('Video generation progress:', progress);
      }
    );

    return NextResponse.json({ videoUrl });
  } catch (error) {
    console.error('Video generation API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Video generation failed' },
      { status: 500 }
    );
  }
}
