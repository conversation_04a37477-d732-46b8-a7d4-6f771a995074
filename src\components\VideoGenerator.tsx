'use client';

import { useState, useRef, useEffect } from 'react';
import { Download, Play, AlertCircle, CheckCircle } from 'lucide-react';
import { FFmpeg } from '@ffmpeg/ffmpeg';
import { toBlobURL } from '@ffmpeg/util';
import type p5 from 'p5'; // MODIFICA: Importa solo i TIPI di p5 per TypeScript, non la libreria

import { VideoGeneratorProps, ProcessingStatus, WaveformStyle } from '@/types';

export default function VideoGenerator({
  audioData,
  style,
  settings,
  visualizationParams,
  onGenerationStart,
  onGenerationComplete
}: VideoGeneratorProps) {
  const [status, setStatus] = useState<ProcessingStatus>('idle');
  const [progress, setProgress] = useState(0);
  const [message, setMessage] = useState('');
  const [videoUrl, setVideoUrl] = useState<string>('');
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);
  const ffmpegRef = useRef<FFmpeg | null>(null);
  const [ffmpegLoaded, setFFmpegLoaded] = useState(false);

  // Cleanup URLs on unmount
  useEffect(() => {
    return () => {
      if (videoUrl) {
        URL.revokeObjectURL(videoUrl);
      }
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [videoUrl, previewUrl]);

  const loadFFmpeg = async () => {
    if (ffmpegRef.current || ffmpegLoaded) return;

    try {
      setMessage('Loading FFmpeg...');
      const ffmpeg = new FFmpeg();

      const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd';

      ffmpeg.on('log', ({ message }) => {
        console.log('FFmpeg log:', message);
      });

      ffmpeg.on('progress', ({ progress }) => {
        if (status === 'encoding_video') {
          setProgress(80 + (progress * 20)); // 80-100% for encoding
        }
      });

      await ffmpeg.load({
        coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
        wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
      });

      ffmpegRef.current = ffmpeg;
      setFFmpegLoaded(true);
      console.log('FFmpeg loaded successfully');
    } catch (error) {
      console.error('Failed to load FFmpeg:', error);
      throw new Error('Failed to load FFmpeg. Please refresh and try again.');
    }
  };
  
  const createP5Instance = async (width: number, height: number): Promise<p5> => {
    // MODIFICA: Carica la libreria p5 dinamicamente solo quando questa funzione viene chiamata.
    const p5Module = await import('p5');
    const P5 = p5Module.default;

    return new Promise((resolve) => {
        const tempContainer = document.createElement('div');
        tempContainer.style.position = 'absolute';
        tempContainer.style.left = '-9999px';
        tempContainer.style.top = '-9999px';
        document.body.appendChild(tempContainer);

        const sketch = (p: p5) => {
            p.setup = () => {
                p.createCanvas(width, height);
                p.colorMode(p.HSB, 360, 100, 100, 100);
                p.noLoop();
                
                const originalRemove = p.remove;
                p.remove = () => {
                    originalRemove.call(p);
                    if (tempContainer.parentNode) {
                        document.body.removeChild(tempContainer);
                    }
                };
                resolve(p);
            };
        };

        new P5(sketch, tempContainer);
    });
  };

  // ... (tutte le funzioni di disegno da drawP5Waveform a drawCircularSpectrum restano INVARIATE) ...
  const drawP5Waveform = (p: any, data: Uint8Array, style: WaveformStyle, time: number, width: number, height: number) => {
    const centerX = width / 2;
    const centerY = height / 2;
    p.background(0);

    switch (style) {
      case 'flowing_waves':
        drawFlowingWaves(p, data, centerX, centerY, time, width, height);
        break;
      case 'bar_equalizer':
        drawBarEqualizer(p, data, centerX, centerY, time, width, height);
        break;
      case 'geometric_patterns':
        drawGeometricPatterns(p, data, centerX, centerY, time);
        break;
      case 'particle_system':
        drawParticleSystem(p, data, centerX, centerY, time);
        break;
      case 'neon_effects':
        drawNeonEffects(p, data, centerX, centerY, time, width, height);
        break;
      case 'minimal_design':
        drawMinimalDesign(p, data, centerX, centerY, time, width, height);
        break;
      case 'energy_burst':
        drawEnergyBurst(p, data, centerX, centerY, time, width, height);
        break;
      case 'circular_spectrum':
        drawCircularSpectrum(p, data, centerX, centerY, time);
        break;
      default:
        drawFlowingWaves(p, data, centerX, centerY, time, width, height);
    }
  };

  const drawFlowingWaves = (p: any, data: Uint8Array, centerX: number, centerY: number, time: number, width: number, height: number) => {
    // Check if data has any meaningful values
    const maxValue = Math.max(...Array.from(data));
    const hasData = maxValue > 0;

    // Calculate overall energy for dynamic effects
    const totalEnergy = Array.from(data).reduce((sum, val) => sum + val, 0) / data.length;
    const energyMultiplier = hasData ? (totalEnergy / 128) * 2 + 0.5 : 1;

    // Create multiple wave layers for richness
    const numLayers = 5;
    const colors = [
      [280, 90, 95], // Bright purple
      [320, 80, 85], // Pink-purple
      [200, 70, 80], // Blue
      [260, 60, 75], // Light purple
      [340, 50, 70]  // Pink
    ];

    for (let layer = 0; layer < numLayers; layer++) {
      const [hue, sat, bright] = colors[layer];
      const alpha = 80 - layer * 15; // Fade out each layer

      p.stroke(hue, sat, bright, alpha);
      p.strokeWeight(4 - layer * 0.5);
      p.noFill();

      p.beginShape();
      for (let i = 0; i < data.length; i++) {
        let amplitude = (data[i] / 255) * (height / 2.5) * energyMultiplier;

        // If no real data, create energetic demo waves
        if (!hasData) {
          const baseWave = p.sin(i * 0.02 + time * (2 + layer * 0.3)) * (height / 4);
          const energyWave = p.sin(time * (3 + layer * 0.5)) * 0.8 + 0.2;
          const detailWave = p.sin(i * 0.1 + time * (4 + layer)) * 0.3;
          amplitude = baseWave * energyWave * (1 + detailWave);
        }

        const x = p.map(i, 0, data.length - 1, 0, width);

        // Add multiple frequency components for more organic movement
        const wave1 = p.sin(i * 0.008 + time * (2.5 + layer * 0.2)) * amplitude * 0.6;
        const wave2 = p.sin(i * 0.015 + time * (1.8 + layer * 0.3)) * amplitude * 0.3;
        const wave3 = p.sin(i * 0.025 + time * (3.2 + layer * 0.1)) * amplitude * 0.1;

        const y = centerY + wave1 + wave2 + wave3;
        p.vertex(x, y);
      }
      p.endShape();

      // Add mirrored wave for symmetry
      p.beginShape();
      for (let i = 0; i < data.length; i++) {
        let amplitude = (data[i] / 255) * (height / 3) * energyMultiplier;

        if (!hasData) {
          const baseWave = p.sin(i * 0.02 + time * (2 + layer * 0.3)) * (height / 5);
          const energyWave = p.sin(time * (3 + layer * 0.5)) * 0.8 + 0.2;
          amplitude = baseWave * energyWave;
        }

        const x = p.map(i, 0, data.length - 1, 0, width);

        const wave1 = p.sin(i * 0.008 + time * (2.5 + layer * 0.2)) * amplitude * 0.6;
        const wave2 = p.sin(i * 0.015 + time * (1.8 + layer * 0.3)) * amplitude * 0.3;

        const y = centerY - (wave1 + wave2) * 0.7; // Slightly smaller mirror
        p.vertex(x, y);
      }
      p.endShape();
    }

    // Add energy burst effects for high-energy moments
    if (hasData && totalEnergy > 180) {
      for (let i = 0; i < 20; i++) {
        const x = p.random(width);
        const y = centerY + p.random(-height/3, height/3);
        const size = p.random(2, 8) * (totalEnergy / 255);

        p.fill(p.random(260, 320), 80, 90, 60);
        p.noStroke();
        p.circle(x, y, size);
      }
    }
  };

  const drawBarEqualizer = (p: any, data: Uint8Array, centerX: number, centerY: number, time: number, width: number, height: number) => {
    const barWidth = width / data.length;

    for (let i = 0; i < data.length; i++) {
      const barHeight = (data[i] / 255) * height * 0.8;
      const x = i * barWidth;
      const y = height - barHeight;

      const hue = (i * 3 + time * 50) % 360;
      p.fill(hue, 70, 90);
      p.noStroke();

      p.rect(x, y, barWidth - 2, barHeight);

      // Add glow effect
      p.fill(hue, 50, 60, 30);
      p.rect(x - 2, y - 2, barWidth + 2, barHeight + 4);
    }
  };

  const drawGeometricPatterns = (p: any, data: Uint8Array, centerX: number, centerY: number, time: number) => {
    // Ensure clean background for geometric patterns
    p.background(0, 0, 0);
    p.translate(centerX, centerY);

    for (let i = 0; i < data.length; i += 4) {
      const amplitude = data[i] / 255;
      if (amplitude > 0.1) {
        const angle = p.map(i, 0, data.length, 0, p.TWO_PI);
        const radius = amplitude * 150;
        const size = amplitude * 30;

        const x = p.cos(angle + time) * radius;
        const y = p.sin(angle + time) * radius;

        p.push();
        p.translate(x, y);
        p.rotate(time + i * 0.1);

        const hue = (i * 5 + time * 30) % 360;
        p.fill(hue, 80, 90);
        p.noStroke();

        p.rect(-size/2, -size/2, size, size);
        p.pop();
      }
    }
    p.resetMatrix();
  };

  const drawMinimalDesign = (p: any, data: Uint8Array, centerX: number, centerY: number, time: number, width: number, height: number) => {
    p.stroke(0, 0, 100);
    p.strokeWeight(1);
    p.noFill();

    p.beginShape();
    for (let i = 0; i < data.length; i++) {
      const amplitude = (data[i] / 255) * (height / 4);
      const x = p.map(i, 0, data.length - 1, 0, width);
      const y = centerY + amplitude;
      p.vertex(x, y);
    }
    p.endShape();

    // Mirror wave
    p.beginShape();
    for (let i = 0; i < data.length; i++) {
      const amplitude = (data[i] / 255) * (height / 4);
      const x = p.map(i, 0, data.length - 1, 0, width);
      const y = centerY - amplitude;
      p.vertex(x, y);
    }
    p.endShape();
  };

  const drawParticleSystem = (p: any, data: Uint8Array, centerX: number, centerY: number, time: number) => {
    // Ensure clean background for particle system
    p.background(0, 0, 0);
    p.translate(centerX, centerY);

    // Check if data has any meaningful values
    const maxValue = Math.max(...Array.from(data));
    const hasData = maxValue > 0;
    const totalEnergy = Array.from(data).reduce((sum, val) => sum + val, 0) / data.length;

    // Create multiple particle rings
    const numRings = 3;

    for (let ring = 0; ring < numRings; ring++) {
      const ringRadius = 80 + ring * 60;
      const particleCount = hasData ? data.length : 64;

      for (let i = 0; i < particleCount; i += 2) {
        const amplitude = hasData ? data[i] / 255 : p.sin(i * 0.1 + time) * 0.5 + 0.5;

        if (amplitude > 0.03) {
          const angle = p.map(i, 0, particleCount, 0, p.TWO_PI * (2 + ring));
          const energyPulse = hasData ? (totalEnergy / 255) : p.sin(time * 2) * 0.5 + 0.5;
          const radius = (amplitude * ringRadius + p.sin(time * (2 + ring * 0.5) + i * 0.1) * 30) * (1 + energyPulse);
          const size = amplitude * (20 + ring * 5) * (1 + energyPulse * 0.5);

          const rotationSpeed = 0.3 + ring * 0.2 + amplitude * 0.5;
          const x = p.cos(angle + time * rotationSpeed) * radius;
          const y = p.sin(angle + time * rotationSpeed) * radius;

          // Dynamic color based on position and energy
          const hue = (i * 3 + time * 80 + ring * 60) % 360;
          const saturation = 60 + amplitude * 40;
          const brightness = 70 + amplitude * 30;
          const alpha = amplitude * 80 + 20;

          p.fill(hue, saturation, brightness, alpha);
          p.noStroke();

          // Main particle
          p.circle(x, y, size);

          // Add glow effect
          p.fill(hue, saturation * 0.5, brightness, alpha * 0.3);
          p.circle(x, y, size * 2);

          // Add trails with multiple segments
          for (let trail = 1; trail <= 3; trail++) {
            const trailAngle = angle + time * rotationSpeed - trail * 0.1;
            const trailRadius = radius * (1 - trail * 0.05);
            const trailX = p.cos(trailAngle) * trailRadius;
            const trailY = p.sin(trailAngle) * trailRadius;
            const trailSize = size * (1 - trail * 0.2);
            const trailAlpha = alpha * (1 - trail * 0.3);

            p.fill(hue, saturation, brightness, trailAlpha);
            p.circle(trailX, trailY, trailSize);
          }

          // Add connecting lines between particles for high energy
          if (hasData && totalEnergy > 150 && i % 8 === 0) {
            const nextI = (i + 8) % particleCount;
            const nextAmplitude = data[nextI] / 255;
            if (nextAmplitude > 0.03) {
              const nextAngle = p.map(nextI, 0, particleCount, 0, p.TWO_PI * (2 + ring));
              const nextRadius = (nextAmplitude * ringRadius + p.sin(time * (2 + ring * 0.5) + nextI * 0.1) * 30) * (1 + energyPulse);
              const nextX = p.cos(nextAngle + time * rotationSpeed) * nextRadius;
              const nextY = p.sin(nextAngle + time * rotationSpeed) * nextRadius;

              p.stroke(hue, saturation, brightness, 30);
              p.strokeWeight(1);
              p.line(x, y, nextX, nextY);
            }
          }
        }
      }
    }
    p.resetMatrix();
  };

  const drawNeonEffects = (p: any, data: Uint8Array, centerX: number, centerY: number, time: number, width: number, height: number) => {
    // Check if data has any meaningful values
    const maxValue = Math.max(...Array.from(data));
    const hasData = maxValue > 0;
    const totalEnergy = Array.from(data).reduce((sum, val) => sum + val, 0) / data.length;

    // Dynamic glow intensity based on energy
    const glowIntensity = hasData ? 20 + (totalEnergy / 255) * 40 : 30;

    // Multiple neon layers with different colors
    const neonLayers = [
      { color: '#ff0080', blur: glowIntensity * 1.5, weight: 6, hue: 320 },
      { color: '#8b5cf6', blur: glowIntensity, weight: 4, hue: 280 },
      { color: '#00ffff', blur: glowIntensity * 0.7, weight: 3, hue: 180 },
      { color: '#ffffff', blur: glowIntensity * 0.3, weight: 1, hue: 0 }
    ];

    neonLayers.forEach((layer, layerIndex) => {
      // Set glow effect
      p.drawingContext.shadowBlur = layer.blur;
      p.drawingContext.shadowColor = layer.color;

      p.stroke(layer.hue, layerIndex === 3 ? 0 : 80, 100);
      p.strokeWeight(layer.weight);
      p.noFill();

      // Main wave
      p.beginShape();
      for (let i = 0; i < data.length; i++) {
        let amplitude = (data[i] / 255) * (height / 2.5);

        if (!hasData) {
          amplitude = (height / 4) * p.sin(i * 0.03 + time * 2) * (0.7 + 0.3 * p.sin(time * 0.8));
        }

        const x = p.map(i, 0, data.length - 1, 0, width);
        const wave1 = p.sin(i * 0.02 + time * (3 + layerIndex * 0.2)) * amplitude;
        const wave2 = p.sin(i * 0.008 + time * (2 - layerIndex * 0.1)) * amplitude * 0.3;
        const y = centerY + wave1 + wave2;
        p.vertex(x, y);
      }
      p.endShape();

      // Mirror wave
      p.beginShape();
      for (let i = 0; i < data.length; i++) {
        let amplitude = (data[i] / 255) * (height / 3);

        if (!hasData) {
          amplitude = (height / 5) * p.sin(i * 0.03 + time * 2) * (0.7 + 0.3 * p.sin(time * 0.8));
        }

        const x = p.map(i, 0, data.length - 1, 0, width);
        const wave1 = p.sin(i * 0.02 + time * (3 + layerIndex * 0.2)) * amplitude;
        const y = centerY - wave1 * 0.8;
        p.vertex(x, y);
      }
      p.endShape();
    });

    // Add electric sparks for high energy moments
    if (hasData && totalEnergy > 200) {
      p.drawingContext.shadowBlur = 15;
      p.drawingContext.shadowColor = '#ffffff';

      for (let i = 0; i < 10; i++) {
        const x = p.random(width);
        const y = centerY + p.random(-height/4, height/4);
        const sparkSize = p.random(1, 4);

        p.stroke(p.random(280, 320), 90, 100);
        p.strokeWeight(sparkSize);
        p.point(x, y);

        // Add small spark lines
        const angle = p.random(p.TWO_PI);
        const length = p.random(5, 15);
        p.line(x, y, x + p.cos(angle) * length, y + p.sin(angle) * length);
      }
    }

    // Reset shadow
    p.drawingContext.shadowBlur = 0;
  };

  const drawEnergyBurst = (p: any, data: Uint8Array, centerX: number, centerY: number, time: number, width: number, height: number) => {
    // Check if data has any meaningful values
    const maxValue = Math.max(...Array.from(data));
    const hasData = maxValue > 0;
    const totalEnergy = Array.from(data).reduce((sum, val) => sum + val, 0) / data.length;
    const energyLevel = hasData ? totalEnergy / 255 : p.sin(time * 2) * 0.5 + 0.5;

    // Dynamic background pulses
    if (energyLevel > 0.3) {
      const pulseAlpha = (energyLevel - 0.3) * 30;
      p.fill(280, 60, 20, pulseAlpha);
      p.noStroke();
      p.circle(centerX, centerY, width * energyLevel);
    }

    // Central energy core
    const coreSize = 20 + energyLevel * 100;
    p.fill(320, 90, 100, 80);
    p.noStroke();
    p.circle(centerX, centerY, coreSize);

    // Core glow
    p.fill(320, 60, 80, 40);
    p.circle(centerX, centerY, coreSize * 2);

    // Energy rays shooting outward
    const numRays = hasData ? Math.min(data.length, 32) : 16;
    for (let i = 0; i < numRays; i++) {
      const amplitude = hasData ? data[i * Math.floor(data.length / numRays)] / 255 : p.sin(i * 0.5 + time) * 0.5 + 0.5;

      if (amplitude > 0.1) {
        const angle = (i / numRays) * p.TWO_PI + time * 0.5;
        const rayLength = amplitude * (width / 3) + p.sin(time * 3 + i) * 20;
        const rayWidth = amplitude * 8 + 2;

        const endX = centerX + p.cos(angle) * rayLength;
        const endY = centerY + p.sin(angle) * rayLength;

        // Ray gradient effect
        const hue = (i * 15 + time * 50) % 360;
        p.stroke(hue, 80, 90, amplitude * 100);
        p.strokeWeight(rayWidth);
        p.line(centerX, centerY, endX, endY);

        // Ray glow
        p.stroke(hue, 60, 70, amplitude * 40);
        p.strokeWeight(rayWidth * 2);
        p.line(centerX, centerY, endX, endY);

        // Energy particles at ray ends
        if (amplitude > 0.5) {
          p.fill(hue, 90, 100, 80);
          p.noStroke();
          p.circle(endX, endY, amplitude * 15);

          // Particle explosion effect
          for (let j = 0; j < 5; j++) {
            const particleAngle = angle + p.random(-0.5, 0.5);
            const particleDistance = rayLength + p.random(10, 30);
            const particleX = centerX + p.cos(particleAngle) * particleDistance;
            const particleY = centerY + p.sin(particleAngle) * particleDistance;
            const particleSize = p.random(2, 8) * amplitude;

            p.fill(hue, 70, 90, amplitude * 60);
            p.circle(particleX, particleY, particleSize);
          }
        }
      }
    }

    // Frequency rings
    const numRings = 4;
    for (let ring = 0; ring < numRings; ring++) {
      const ringRadius = 50 + ring * 40 + energyLevel * 30;
      const ringThickness = 2 + energyLevel * 3;

      p.noFill();
      p.stroke(280 + ring * 20, 70, 80, 60 - ring * 10);
      p.strokeWeight(ringThickness);

      // Animated ring segments
      const segments = 8;
      for (let seg = 0; seg < segments; seg++) {
        const startAngle = (seg / segments) * p.TWO_PI + time * (1 + ring * 0.2);
        const endAngle = startAngle + (p.PI / segments) * (0.5 + energyLevel);

        p.arc(centerX, centerY, ringRadius * 2, ringRadius * 2, startAngle, endAngle);
      }
    }

    // Bass frequency visualization (low frequencies create ground shakes)
    if (hasData && data.length > 10) {
      const bassEnergy = Array.from(data.slice(0, 10)).reduce((sum, val) => sum + val, 0) / 10 / 255;
      if (bassEnergy > 0.3) {
        const shakeIntensity = (bassEnergy - 0.3) * 20;

        // Screen shake effect simulation with multiple lines
        for (let i = 0; i < 5; i++) {
          const shakeX = p.random(-shakeIntensity, shakeIntensity);
          const shakeY = p.random(-shakeIntensity, shakeIntensity);

          p.stroke(40, 90, 90, 30);
          p.strokeWeight(3);
          p.line(0, centerY + shakeY, width, centerY + shakeY);
          p.line(centerX + shakeX, 0, centerX + shakeX, height);
        }
      }
    }
  };

  const drawCircularSpectrum = (p: any, data: Uint8Array, centerX: number, centerY: number, time: number) => {
    // Check if data has any meaningful values
    const maxValue = Math.max(...Array.from(data));
    const hasData = maxValue > 0;
    const totalEnergy = Array.from(data).reduce((sum, val) => sum + val, 0) / data.length;

    // Circle parameters
    const baseRadius = 80;
    const maxBarLength = 120;
    const numBars = hasData ? Math.min(data.length, 128) : 64; // Limit for performance

    // Draw frequency bars in a circle
    for (let i = 0; i < numBars; i++) {
      // Calculate angle with rotation
      const angle = (i / numBars) * p.TWO_PI + time * 0.2;
      let amplitude = hasData ? data[Math.floor(i * data.length / numBars)] / 255 :
                     p.sin(i * 0.1 + time * 2) * 0.5 + 0.5;

      // Smooth amplitude changes to prevent flickering
      amplitude = p.constrain(amplitude, 0, 1);

      const barLength = amplitude * maxBarLength * visualizationParams.sensitivity;
      const barWidth = Math.max(2, (p.TWO_PI / numBars) * baseRadius * 0.6); // Bar thickness

      // Calculate bar position relative to center
      const startX = centerX + p.cos(angle) * baseRadius;
      const startY = centerY + p.sin(angle) * baseRadius;
      const endX = centerX + p.cos(angle) * (baseRadius + barLength);
      const endY = centerY + p.sin(angle) * (baseRadius + barLength);

      // Color based on frequency position and amplitude
      const hue = (i * 360 / numBars + time * 30) % 360;
      const saturation = 70 + amplitude * 30;
      const brightness = 60 + amplitude * 40;
      const alpha = 80 + amplitude * 20;

      // Draw main bar
      p.stroke(hue, saturation, brightness, alpha);
      p.strokeWeight(barWidth);
      p.line(startX, startY, endX, endY);

      // Add glow effect for higher amplitudes
      if (amplitude > 0.3) {
        p.stroke(hue, saturation * 0.6, brightness, alpha * 0.4);
        p.strokeWeight(barWidth * 1.5);
        p.line(startX, startY, endX, endY);
      }

      // Add inner glow
      if (amplitude > 0.6) {
        p.stroke(hue, 40, 90, alpha * 0.6);
        p.strokeWeight(barWidth * 2);
        p.line(startX, startY, endX, endY);
      }

      // Add particle effects at bar tips for high energy
      if (amplitude > 0.7) {
        p.fill(hue, saturation, brightness, amplitude * 100);
        p.noStroke();
        const particleSize = amplitude * 8;
        p.circle(endX, endY, particleSize);

        // Add sparkle effect
        p.fill(0, 0, 100, amplitude * 80);
        p.circle(endX, endY, particleSize * 0.5);
      }
    }

    // Draw center circle
    p.noStroke();
    const centerSize = 20 + (totalEnergy / 255) * 30;

    // Center circle with gradient effect
    for (let r = centerSize; r > 0; r -= 2) {
      const alpha = p.map(r, 0, centerSize, 100, 0);
      const hue = (time * 50) % 360;
      p.fill(hue, 60, 90, alpha);
      p.circle(centerX, centerY, r);
    }

    // Add pulsing ring around center
    const pulseRadius = centerSize + p.sin(time * 4) * 10;
    p.noFill();
    p.stroke(280, 80, 90, 60);
    p.strokeWeight(2);
    p.circle(centerX, centerY, pulseRadius);
  };


  const generatePreview = async () => {
    // ... (il resto della funzione è identico a prima, usando il p5Instance corretto)
    if (isGeneratingPreview || isGenerating) return;

    setIsGeneratingPreview(true);
    setStatus('generating_frames');
    setProgress(0);
    setMessage('Generating 3-second preview...');
    
    let p5Instance: p5 | null = null;

    try {
      if (!ffmpegLoaded) {
        setMessage('Loading FFmpeg...');
        await loadFFmpeg();
      }
      
      const [width, height] = settings.resolution.split('x').map(Number);
      
      p5Instance = await createP5Instance(width, height);

      setMessage('Generating preview frames...');
      
      const previewDuration = Math.min(3, audioData.metadata.duration);
      const totalFrames = Math.floor(previewDuration * settings.fps);
      console.log(`Generating ${totalFrames} frames for ${previewDuration}s preview at ${settings.fps}fps`);
      
      const frames: string[] = [];

      for (let frame = 0; frame < totalFrames; frame++) {
        const timeProgress = frame / totalFrames;
        const currentTime = timeProgress * previewDuration;

        const frameIndex = Math.floor((currentTime / audioData.metadata.duration) * audioData.frequencyData.length);
        const frequencyFrame = audioData.frequencyData[frameIndex] || new Float32Array(128);

        const audioDataArray = new Uint8Array(frequencyFrame.length);
        for (let i = 0; i < frequencyFrame.length; i++) {
          audioDataArray[i] = Math.min(255, Math.max(10, frequencyFrame[i] * 100));
        }
        
        const animationTime = currentTime * 2.5;

        drawP5Waveform(p5Instance, audioDataArray, style, animationTime, width, height);
        const frameDataUrl = (p5Instance.drawingContext.canvas as HTMLCanvasElement).toDataURL('image/png');
        frames.push(frameDataUrl);

        const frameProgress = (frame / totalFrames) * 60;
        setProgress(frameProgress);
        setMessage(`Generating preview frame ${frame + 1} of ${totalFrames}...`);

        if (frame % 5 === 0) {
          await new Promise(resolve => setTimeout(resolve, 1));
        }
      }

      setMessage('Preparing preview frames...');
      setProgress(65);
      // ... (resto della funzione invariato)
       const frameFiles: { name: string; data: Uint8Array }[] = [];

      for (let i = 0; i < frames.length; i++) {
        const response = await fetch(frames[i]);
        const blob = await response.blob();
        const arrayBuffer = await blob.arrayBuffer();
        const uint8Array = new Uint8Array(arrayBuffer);

        const frameName = `preview_frame_${i.toString().padStart(6, '0')}.png`;
        frameFiles.push({ name: frameName, data: uint8Array });

        if (i % 10 === 0) {
          setProgress(65 + (i / frames.length) * 10);
          await new Promise(resolve => setTimeout(resolve, 1));
        }
      }

      setMessage('Preparing preview audio...');
      setProgress(80);

      const createPreviewWav = (audioBuffer: AudioBuffer, duration: number): Uint8Array => {
        const numberOfChannels = audioBuffer.numberOfChannels;
        const sampleRate = audioBuffer.sampleRate;
        const previewSamples = Math.floor(duration * sampleRate);
        const bytesPerSample = 2;
        const blockAlign = numberOfChannels * bytesPerSample;
        const byteRate = sampleRate * blockAlign;
        const dataSize = previewSamples * blockAlign;
        const bufferSize = 44 + dataSize;
        const arrayBuffer = new ArrayBuffer(bufferSize);
        const view = new DataView(arrayBuffer);
        const writeString = (offset: number, string: string) => {
          for (let i = 0; i < string.length; i++) {
            view.setUint8(offset + i, string.charCodeAt(i));
          }
        };
        writeString(0, 'RIFF');
        view.setUint32(4, bufferSize - 8, true);
        writeString(8, 'WAVE');
        writeString(12, 'fmt ');
        view.setUint32(16, 16, true);
        view.setUint16(20, 1, true);
        view.setUint16(22, numberOfChannels, true);
        view.setUint32(24, sampleRate, true);
        view.setUint32(28, byteRate, true);
        view.setUint16(32, blockAlign, true);
        view.setUint16(34, 16, true);
        writeString(36, 'data');
        view.setUint32(40, dataSize, true);
        let offset = 44;
        for (let i = 0; i < previewSamples; i++) {
          for (let channel = 0; channel < numberOfChannels; channel++) {
            const sample = Math.max(-1, Math.min(1, audioBuffer.getChannelData(channel)[i]));
            view.setInt16(offset, sample * 0x7FFF, true);
            offset += 2;
          }
        }
        return new Uint8Array(arrayBuffer);
      };
      const previewAudioWav = createPreviewWav(audioData.audioBuffer, previewDuration);
      setStatus('encoding_video');
      setMessage('Encoding preview video...');
      setProgress(85);
      const ffmpeg = ffmpegRef.current!;
      await ffmpeg.writeFile('preview_audio.wav', previewAudioWav);
      for (let i = 0; i < frameFiles.length; i++) {
        await ffmpeg.writeFile(frameFiles[i].name, frameFiles[i].data);
        if (i % 20 === 0) {
          setProgress(85 + (i / frameFiles.length) * 10);
        }
      }
      setMessage('Finalizing preview...');
      setProgress(95);
      const previewOutputName = 'preview_output.mp4';
      await ffmpeg.exec([
        '-framerate', settings.fps.toString(),
        '-i', 'preview_frame_%06d.png',
        '-i', 'preview_audio.wav',
        '-c:v', 'libx264',
        '-c:a', 'aac',
        '-pix_fmt', 'yuv420p',
        '-shortest',
        previewOutputName
      ]);
      const previewData = await ffmpeg.readFile(previewOutputName);
      const previewBlob = new Blob([previewData], { type: 'video/mp4' });
      const previewVideoUrl = URL.createObjectURL(previewBlob);
      setPreviewUrl(previewVideoUrl);
      setProgress(100);
      setStatus('complete');
      setMessage('Preview generated successfully!');
    } catch (error) {
      console.error('Preview generation error:', error);
      setStatus('error');
      setMessage(error instanceof Error ? error.message : 'Failed to generate preview');
    } finally {
      if (p5Instance) {
        p5Instance.remove();
      }
      setIsGeneratingPreview(false);
    }
  };

  const generateVideo = async () => {
    // ... (il resto della funzione è identico a prima, usando il p5Instance corretto)
    if (isGenerating || isGeneratingPreview) return;

    if (audioData.metadata.duration > 900) {
      setStatus('error');
      setMessage('Audio too long. Maximum duration is 15 minutes for video generation.');
      return;
    }

    setIsGenerating(true);
    onGenerationStart();
    setStatus('generating_frames');
    setProgress(0);
    setMessage('Preparing full video generation...');

    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl('');
    }

    let p5Instance: p5 | null = null;

    try {
      setMessage('Analyzing audio frequencies...');
      await new Promise(resolve => setTimeout(resolve, 100));
      setProgress(10);
      
      const [width, height] = settings.resolution.split('x').map(Number);
      
      p5Instance = await createP5Instance(width, height);

      const totalFrames = Math.floor(audioData.metadata.duration * settings.fps);
      console.log(`Generating ${totalFrames} frames for ${audioData.metadata.duration}s video at ${settings.fps}fps`);
      
      const frames: string[] = [];
      for (let frame = 0; frame < totalFrames; frame++) {
        const timeProgress = frame / totalFrames;
        const currentTime = timeProgress * audioData.metadata.duration;

        const frameIndex = Math.floor(timeProgress * audioData.frequencyData.length);
        const frequencyFrame = audioData.frequencyData[frameIndex] || new Float32Array(128);

        const audioDataArray = new Uint8Array(frequencyFrame.length);
        for (let i = 0; i < frequencyFrame.length; i++) {
          audioDataArray[i] = Math.min(255, Math.max(10, frequencyFrame[i] * 100));
        }

        const animationTime = currentTime * 2.5;

        drawP5Waveform(p5Instance, audioDataArray, style, animationTime, width, height);
        const frameDataUrl = (p5Instance.drawingContext.canvas as HTMLCanvasElement).toDataURL('image/png');
        frames.push(frameDataUrl);

        const frameProgress = (frame / totalFrames) * 70;
        setProgress(10 + frameProgress);
        setMessage(`Generating frame ${frame + 1} of ${totalFrames}...`);

        if (frame % 10 === 0) {
          await new Promise(resolve => setTimeout(resolve, 1));
        }
      }

      if (!ffmpegLoaded) {
        setMessage('Loading FFmpeg...');
        await loadFFmpeg();
      }
      setMessage('Preparing frames for encoding...');
      setProgress(82);
      // ... (resto della funzione invariato)
      const frameFiles: { name: string; data: Uint8Array }[] = [];
      for (let i = 0; i < frames.length; i++) {
        const response = await fetch(frames[i]);
        const blob = await response.blob();
        const arrayBuffer = await blob.arrayBuffer();
        const uint8Array = new Uint8Array(arrayBuffer);
        const frameName = `frame_${i.toString().padStart(6, '0')}.png`;
        frameFiles.push({ name: frameName, data: uint8Array });
        if (i % 50 === 0) {
          setProgress(82 + (i / frames.length) * 3);
          await new Promise(resolve => setTimeout(resolve, 1));
        }
      }
      setMessage('Preparing audio...');
      setProgress(85);
      const createWavFile = (audioBuffer: AudioBuffer): Uint8Array => {
        const numberOfChannels = audioBuffer.numberOfChannels;
        const length = audioBuffer.length;
        const sampleRate = audioBuffer.sampleRate;
        const bytesPerSample = 2;
        const blockAlign = numberOfChannels * bytesPerSample;
        const byteRate = sampleRate * blockAlign;
        const dataSize = length * blockAlign;
        const bufferSize = 44 + dataSize;
        const arrayBuffer = new ArrayBuffer(bufferSize);
        const view = new DataView(arrayBuffer);
        const writeString = (offset: number, string: string) => {
          for (let i = 0; i < string.length; i++) {
            view.setUint8(offset + i, string.charCodeAt(i));
          }
        };
        writeString(0, 'RIFF');
        view.setUint32(4, bufferSize - 8, true);
        writeString(8, 'WAVE');
        writeString(12, 'fmt ');
        view.setUint32(16, 16, true);
        view.setUint16(20, 1, true);
        view.setUint16(22, numberOfChannels, true);
        view.setUint32(24, sampleRate, true);
        view.setUint32(28, byteRate, true);
        view.setUint16(32, blockAlign, true);
        view.setUint16(34, 16, true);
        writeString(36, 'data');
        view.setUint32(40, dataSize, true);
        let offset = 44;
        for (let i = 0; i < length; i++) {
          for (let channel = 0; channel < numberOfChannels; channel++) {
            const sample = Math.max(-1, Math.min(1, audioBuffer.getChannelData(channel)[i]));
            view.setInt16(offset, sample * 0x7FFF, true);
            offset += 2;
          }
        }
        return new Uint8Array(arrayBuffer);
      };
      const audioWav = createWavFile(audioData.audioBuffer);
      setStatus('encoding_video');
      setMessage('Encoding video with FFmpeg...');
      setProgress(88);
      const ffmpeg = ffmpegRef.current!;
      await ffmpeg.writeFile('audio.wav', audioWav);
      setMessage('Writing frames to FFmpeg...');
      for (let i = 0; i < frameFiles.length; i++) {
        await ffmpeg.writeFile(frameFiles[i].name, frameFiles[i].data);
        if (i % 100 === 0) {
          setProgress(88 + (i / frameFiles.length) * 5);
        }
      }
      setMessage('Encoding final video...');
      setProgress(93);
      const outputName = 'output.mp4';
      await ffmpeg.exec([
        '-framerate', settings.fps.toString(),
        '-i', 'frame_%06d.png',
        '-i', 'audio.wav',
        '-c:v', 'libx264',
        '-c:a', 'aac',
        '-pix_fmt', 'yuv420p',
        '-shortest',
        outputName
      ]);
      const data = await ffmpeg.readFile(outputName);
      const videoBlob = new Blob([data], { type: 'video/mp4' });
      const url = URL.createObjectURL(videoBlob);
      setVideoUrl(url);
      setProgress(100);
      setStatus('complete');
      setMessage('Video generated successfully!');

    } catch (error) {
      console.error('Video generation error:', error);
      setStatus('error');
      setMessage(error instanceof Error ? error.message : 'Failed to generate video');
    } finally {
      if (p5Instance) {
        p5Instance.remove();
      }
      setIsGenerating(false);
      onGenerationComplete();
    }
  };

  const downloadVideo = () => {
    if (videoUrl) {
      const a = document.createElement('a');
      a.href = videoUrl;
      a.download = `${audioData.metadata.fileName.replace(/\.[^/.]+$/, '')}-visualization.mp4`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'generating_frames':
      case 'encoding_video':
        return <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full" />;
      case 'complete':
        return <CheckCircle className="w-4 h-4" />;
      case 'error':
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <Play className="w-4 h-4" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'complete':
        return 'text-green-400';
      case 'error':
        return 'text-red-400';
      default:
        return 'text-white';
    }
  };

  return (
    // ... (JSX invariato)
    <div className="space-y-4">
      {/* Preview and Generation buttons */}
      <div className="space-y-3">
        {/* Preview button */}
        <button
          onClick={generatePreview}
          disabled={isGenerating || isGeneratingPreview}
          className={`
            w-full py-3 px-6 rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2
            ${isGenerating || isGeneratingPreview
              ? 'bg-gray-600 cursor-not-allowed text-gray-400'
              : 'bg-blue-600 hover:bg-blue-700 active:scale-95 text-white'
            }
          `}
        >
          <Play className="w-5 h-5" />
          <span>
            {isGeneratingPreview ? 'Generating Preview...' : 'Generate 3s Preview'}
          </span>
        </button>

        {/* Full video generation button */}
        <button
          onClick={generateVideo}
          disabled={isGenerating || isGeneratingPreview}
          className={`
            w-full py-3 px-6 rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2
            ${isGenerating || isGeneratingPreview
              ? 'bg-gray-600 cursor-not-allowed'
              : 'bg-purple-600 hover:bg-purple-700 active:scale-95'
            }
          `}
        >
          {getStatusIcon()}
          <span className={getStatusColor()}>
            {isGenerating ? 'Generating Full Video...' : 'Generate Full Video'}
          </span>
        </button>
      </div>

      {/* Preview video */}
      {previewUrl && !isGenerating && (
        <div className="space-y-3">
          <h3 className="text-lg font-medium text-white">Preview (3 seconds)</h3>
          <video
            src={previewUrl}
            controls
            className="w-full rounded-lg bg-black"
            style={{ maxHeight: '300px' }}
          >
            Your browser does not support the video tag.
          </video>
          <button
            onClick={() => {
              const a = document.createElement('a');
              a.href = previewUrl;
              a.download = `preview-${audioData.metadata.fileName.replace(/\.[^/.]+$/, '')}.mp4`;
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
            }}
            className="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2"
          >
            <Download className="w-4 h-4" />
            <span>Download Preview</span>
          </button>
        </div>
      )}

      {/* Progress indicator */}
      {(isGenerating || isGeneratingPreview) && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-300">{message}</span>
            <span className="text-gray-300">{Math.round(progress)}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div 
              className="bg-purple-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>
      )}

      {/* Status message */}
      {status !== 'idle' && !isGenerating && !isGeneratingPreview && (
        <div className={`text-center py-2 ${getStatusColor()}`}>
          {message}
        </div>
      )}

      {/* Download button */}
      {status === 'complete' && videoUrl && (
        <button
          onClick={downloadVideo}
          className="w-full py-3 px-6 bg-green-600 hover:bg-green-700 rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2"
        >
          <Download className="w-5 h-5" />
          <span>Download Video</span>
        </button>
      )}

      {/* Video info */}
      {status === 'complete' && videoUrl && !isGenerating && (
        <div className="text-sm text-gray-400 space-y-1">
          <p>Resolution: {settings.resolution}</p>
          <p>Frame Rate: {settings.fps} FPS</p>
          <p>Duration: {Math.round(audioData.metadata.duration)}s</p>
          <p>Style: {style.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</p>
        </div>
      )}
    </div>
  );
}