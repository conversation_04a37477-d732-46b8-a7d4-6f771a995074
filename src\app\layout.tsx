import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { Analytics } from "@vercel/analytics/next";
import StructuredData from "@/components/StructuredData";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Free Audio Wave Generator - Beautiful Waveform Video Creator",
  description: "Free online tool to transform your audio files into stunning waveform videos with customizable styles and effects. Create professional music visualizations in minutes - no registration required!",
  keywords: ["free audio wave generator", "free wave form generator", "free audio visualization", "free waveform generator", "free video generator", "free music visualization", "free audio to video", "sound visualization", "audio waveform", "sound wave generator", "no registration"],
  authors: [{ name: "Audio Wave Generator" }],
  metadataBase: new URL('https://www.audiowavegenerator.com'),
  openGraph: {
    title: "Free Audio Wave Generator - Beautiful Waveform Video Creator",
    description: "Free online tool to transform your audio files into stunning waveform videos with customizable styles and effects. Create professional music visualizations in minutes - no registration required!",
    url: 'https://www.audiowavegenerator.com',
    siteName: 'Audio Wave Generator',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "Free Audio Wave Generator - Professional Waveform Video Creator",
    description: "Free online tool to transform your audio files into stunning waveform videos with customizable styles and effects. Create professional music visualizations in minutes - no registration required!",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" type="image/png" href="/logo-audiowavegenerator.png" />
        <link rel="apple-touch-icon" href="/logo-audiowavegenerator.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Free Audio Wave Generator" />
        <meta name="mobile-web-app-capable" content="yes" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <StructuredData />
        {children}
        <Analytics />
      </body>
    </html>
  );
}
