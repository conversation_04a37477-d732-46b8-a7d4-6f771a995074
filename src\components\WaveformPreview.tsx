'use client';

import { useEffect, useRef, useState } from 'react';
import { Play, Pause, Volume2 } from 'lucide-react';
import { WaveformPreviewProps } from '@/types';
import P5Sketch from './P5Sketch';

export default function WaveformPreview({ audioData, style, visualizationParams }: WaveformPreviewProps) {
  const audioRef = useRef<HTMLAudioElement>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const sourceRef = useRef<MediaElementAudioSourceNode | null>(null);

  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [volume, setVolume] = useState(0.7);
  const [audioUrl, setAudioUrl] = useState<string>('');
  const [currentAudioData, setCurrentAudioData] = useState<Uint8Array>(new Uint8Array(128));

  // Create audio URL from buffer
  useEffect(() => {
    if (audioData.audioBuffer) {
      // Create a simple WAV file from AudioBuffer
      const createWavBlob = (audioBuffer: AudioBuffer): Blob => {
        const numberOfChannels = audioBuffer.numberOfChannels;
        const length = audioBuffer.length;
        const sampleRate = audioBuffer.sampleRate;
        const bytesPerSample = 2;
        const blockAlign = numberOfChannels * bytesPerSample;
        const byteRate = sampleRate * blockAlign;
        const dataSize = length * blockAlign;
        const bufferSize = 44 + dataSize;

        const arrayBuffer = new ArrayBuffer(bufferSize);
        const view = new DataView(arrayBuffer);

        // WAV header
        const writeString = (offset: number, string: string) => {
          for (let i = 0; i < string.length; i++) {
            view.setUint8(offset + i, string.charCodeAt(i));
          }
        };

        writeString(0, 'RIFF');
        view.setUint32(4, bufferSize - 8, true);
        writeString(8, 'WAVE');
        writeString(12, 'fmt ');
        view.setUint32(16, 16, true);
        view.setUint16(20, 1, true);
        view.setUint16(22, numberOfChannels, true);
        view.setUint32(24, sampleRate, true);
        view.setUint32(28, byteRate, true);
        view.setUint16(32, blockAlign, true);
        view.setUint16(34, 16, true);
        writeString(36, 'data');
        view.setUint32(40, dataSize, true);

        // Convert audio data
        let offset = 44;
        for (let i = 0; i < length; i++) {
          for (let channel = 0; channel < numberOfChannels; channel++) {
            const sample = Math.max(-1, Math.min(1, audioBuffer.getChannelData(channel)[i]));
            view.setInt16(offset, sample * 0x7FFF, true);
            offset += 2;
          }
        }

        return new Blob([arrayBuffer], { type: 'audio/wav' });
      };

      const blob = createWavBlob(audioData.audioBuffer);
      const url = URL.createObjectURL(blob);
      setAudioUrl(url);

      return () => {
        URL.revokeObjectURL(url);
      };
    }
  }, [audioData.audioBuffer]);

  // Setup audio analysis
  useEffect(() => {
    if (audioRef.current && !audioContextRef.current) {
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
      analyserRef.current = audioContextRef.current.createAnalyser();

      // Optimize analyser settings for better frequency response
      analyserRef.current.fftSize = 256; // 128 frequency bins
      analyserRef.current.smoothingTimeConstant = 0.3; // Less smoothing for more responsive visualization
      analyserRef.current.minDecibels = -90; // Lower threshold for better sensitivity
      analyserRef.current.maxDecibels = -10; // Higher threshold for better range

      sourceRef.current = audioContextRef.current.createMediaElementSource(audioRef.current);
      sourceRef.current.connect(analyserRef.current);
      analyserRef.current.connect(audioContextRef.current.destination);

      console.log('Audio analyser setup complete:', {
        fftSize: analyserRef.current.fftSize,
        frequencyBinCount: analyserRef.current.frequencyBinCount,
        smoothingTimeConstant: analyserRef.current.smoothingTimeConstant
      });
    }
  }, [audioUrl]);

  // Update audio data for p5.js visualization with high-frequency updates
  // 🔧 FIX: Changed from 10Hz setInterval to 60fps requestAnimationFrame for smooth preview
  useEffect(() => {
    let lastUpdateTime = 0;
    let lastCurrentTime = -1; // Cache for avoiding redundant calculations
    const targetFPS = 60; // Target 60 FPS to match p5.js rendering
    const frameInterval = 1000 / targetFPS; // ~16.67ms between frames

    const updateAudioData = () => {
      let dataArray: Uint8Array;

      if (analyserRef.current) {
        // Always use real-time audio analysis when available
        const bufferLength = analyserRef.current.frequencyBinCount; // Should be 128
        const tempDataArray = new Uint8Array(bufferLength);
        analyserRef.current.getByteFrequencyData(tempDataArray);

        // 🔧 FIX 2: Create a symmetrical ("palindrome") visualization for a seamless circular loop.
        const VISUAL_BINS = 128;
        const HALF_VISUAL_BINS = VISUAL_BINS / 2; // We'll work with half the circle: 64 bins
        dataArray = new Uint8Array(VISUAL_BINS);

        for (let i = 0; i < HALF_VISUAL_BINS; i++) {
          // We only read from the first half of the source data, which is the most active.
          const value = tempDataArray[i];

          // Ensure a minimum floor value to keep bars alive.
          const activeValue = Math.max(2, value);

          // Apply the value to the first half of the circle.
          dataArray[i] = activeValue;
          // Apply the *same* value to the corresponding mirrored position in the second half.
          dataArray[VISUAL_BINS - 1 - i] = activeValue;
        }

        // If the audio is paused, the analyser will return mostly zeros
        // In that case, fall back to pre-analyzed data
        const maxVal = Math.max(...Array.from(dataArray));

        if (maxVal < 5 && !isPlaying) {
          // Fallback to pre-analyzed data when audio is paused
          // 🚀 OPTIMIZATION: Cache calculation to avoid redundant work
          if (Math.abs(currentTime - lastCurrentTime) < 0.01) {
            // Time hasn't changed significantly, skip recalculation
            return;
          }
          lastCurrentTime = currentTime;

          const frameIndex = Math.floor((currentTime / audioData.metadata.duration) * audioData.frequencyData.length);
          const frequencyFrame = audioData.frequencyData[frameIndex] || audioData.frequencyData[0] || new Float32Array(128);

          // 🔧 FIX: Always ensure exactly 128 bins for consistent circular spectrum
          dataArray = new Uint8Array(128);

          // Find the maximum value in the frame for dynamic scaling
          let maxValue = 0;
          for (let i = 0; i < frequencyFrame.length && i < 128; i++) {
            maxValue = Math.max(maxValue, frequencyFrame[i]);
          }

          // Apply dynamic scaling with a minimum threshold
          const scaleFactor = maxValue > 0 ? Math.min(255, 100 / maxValue) : 100;

          // 🔧 FIX: Fill all 128 bins, interpolating if necessary
          for (let i = 0; i < 128; i++) {
            let value;
            if (i < frequencyFrame.length) {
              // Use direct mapping for available data
              value = frequencyFrame[i] * scaleFactor;
            } else {
              // Interpolate or use fallback for missing data
              const fallbackIndex = Math.floor((i / 128) * frequencyFrame.length);
              value = frequencyFrame[fallbackIndex] * scaleFactor;
            }
            dataArray[i] = Math.min(255, Math.max(10, value));
          }
        }
      } else {
        // Fallback when no analyser is available
        const frameIndex = Math.floor((currentTime / audioData.metadata.duration) * audioData.frequencyData.length);
        const frequencyFrame = audioData.frequencyData[frameIndex] || audioData.frequencyData[0] || new Float32Array(128);

        // 🔧 FIX: Always ensure exactly 128 bins for consistent circular spectrum
        dataArray = new Uint8Array(128);

        // Fill all 128 bins, interpolating if necessary
        for (let i = 0; i < 128; i++) {
          let value;
          if (i < frequencyFrame.length) {
            value = frequencyFrame[i] * 100;
          } else {
            // Interpolate missing data
            const sourceIndex = Math.floor((i / 128) * frequencyFrame.length);
            value = frequencyFrame[sourceIndex] * 100;
          }
          dataArray[i] = Math.min(255, Math.max(10, value));
        }
      }

      // Debug: log data info occasionally
      if (Math.random() < 0.001) { // Log 0.1% of the time to avoid spam
        const maxVal = Math.max(...Array.from(dataArray));
        const avgVal = Array.from(dataArray).reduce((a, b) => a + b, 0) / dataArray.length;
        console.log('Audio data debug:', {
          isPlaying,
          currentTime: currentTime.toFixed(2),
          maxValue: maxVal,
          avgValue: avgVal.toFixed(2),
          dataLength: dataArray.length,
          hasAnalyser: !!analyserRef.current,
          frameIndex: Math.floor((currentTime / audioData.metadata.duration) * audioData.frequencyData.length)
        });
      }

      setCurrentAudioData(dataArray);
    };

    // Use requestAnimationFrame for smooth 60fps updates with intelligent throttling
    let animationFrameId: number;

    const scheduleUpdate = () => {
      const currentTime = performance.now();

      // Throttle updates to target FPS only when necessary (e.g., when not playing)
      if (!isPlaying || currentTime - lastUpdateTime >= frameInterval) {
        updateAudioData();
        lastUpdateTime = currentTime;
      }

      animationFrameId = requestAnimationFrame(scheduleUpdate);
    };

    // Start the animation loop
    animationFrameId = requestAnimationFrame(scheduleUpdate);

    return () => {
      // Cancel the animation frame when component unmounts or dependencies change
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    };
  }, [isPlaying, currentTime, audioData]);



  const togglePlayback = async () => {
    if (!audioRef.current) return;

    if (isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    } else {
      try {
        if (audioContextRef.current?.state === 'suspended') {
          await audioContextRef.current.resume();
        }
        await audioRef.current.play();
        setIsPlaying(true);
      } catch (error) {
        console.error('Error playing audio:', error);
      }
    }
  };

  // Update audio volume when volume state changes
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = volume;
    }
  }, [volume]);

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (audioRef.current) {
      const time = parseFloat(e.target.value);
      audioRef.current.currentTime = time;
      setCurrentTime(time);
    }
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    if (audioRef.current) {
      audioRef.current.volume = newVolume;
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-4">
      {/* P5.js waveform visualization */}
      <div className="relative bg-black rounded-lg overflow-hidden" style={{ width: '100%', height: '256px' }}>
        <P5Sketch
          audioData={currentAudioData}
          style={style}
          width={800}
          height={256}
          isPlaying={isPlaying}
          visualizationParams={visualizationParams}
        />
      </div>

      {/* Audio element (hidden) */}
      {audioUrl && (
        <audio
          ref={audioRef}
          src={audioUrl}
          onTimeUpdate={handleTimeUpdate}
          onEnded={() => setIsPlaying(false)}
        />
      )}

      {/* Audio controls */}
      <div className="space-y-3">
        {/* Play/Pause and Time */}
        <div className="flex items-center space-x-4">
          <button
            onClick={togglePlayback}
            className="flex items-center justify-center w-10 h-10 bg-purple-600 hover:bg-purple-700 rounded-full transition-colors"
          >
            {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5 ml-0.5" />}
          </button>
          
          <div className="flex-1">
            <input
              type="range"
              min={0}
              max={audioData.metadata.duration}
              value={currentTime}
              onChange={handleSeek}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
            />
          </div>
          
          <span className="text-sm text-gray-300 min-w-[80px]">
            {formatTime(currentTime)} / {formatTime(audioData.metadata.duration)}
          </span>
        </div>

        {/* Volume control */}
        <div className="flex items-center space-x-3">
          <Volume2 className="w-4 h-4 text-gray-400" />
          <input
            type="range"
            min={0}
            max={1}
            step={0.1}
            value={volume}
            onChange={handleVolumeChange}
            className="w-24 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
          />
          <span className="text-sm text-gray-400 min-w-[30px]">
            {Math.round(volume * 100)}%
          </span>
        </div>
      </div>
    </div>
  );
}
