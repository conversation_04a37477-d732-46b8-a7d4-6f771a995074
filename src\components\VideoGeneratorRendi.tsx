'use client';

import { useState, useRef, useEffect } from 'react';
import { Download, Play, AlertCircle, CheckCircle, Cloud, Zap } from 'lucide-react';
import { rendiService, VideoGenerationProgress } from '@/services/rendiService';
import { supabaseService, UploadProgress } from '@/services/supabaseService';
import { VideoGeneratorProps, ProcessingStatus } from '@/types';

export default function VideoGeneratorRendi({
  audioData,
  style,
  settings,
  visualizationParams,
  onGenerationStart,
  onGenerationComplete
}: VideoGeneratorProps) {
  const [status, setStatus] = useState<ProcessingStatus>('idle');
  const [progress, setProgress] = useState(0);
  const [message, setMessage] = useState('');
  const [videoUrl, setVideoUrl] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [commandId, setCommandId] = useState<string>('');
  const [uploadedFilePath, setUploadedFilePath] = useState<string>('');
  const abortControllerRef = useRef<AbortController | null>(null);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Cleanup uploaded file when component unmounts
      if (uploadedFilePath) {
        supabaseService.deleteAudioFile(uploadedFilePath).catch(console.error);
      }
    };
  }, [uploadedFilePath]);

  const handleUploadProgress = (uploadProgress: UploadProgress) => {
    setProgress(uploadProgress.progress * 0.2); // Upload takes 20% of total progress
    setMessage(uploadProgress.message);
    
    if (uploadProgress.status === 'error') {
      setStatus('error');
      setIsGenerating(false);
    }
  };

  const handleVideoProgress = (videoProgress: VideoGenerationProgress) => {
    // Video generation takes 80% of total progress (20-100%)
    setProgress(20 + (videoProgress.progress * 0.8));
    setMessage(videoProgress.message);
    
    if (videoProgress.commandId) {
      setCommandId(videoProgress.commandId);
    }
    
    switch (videoProgress.status) {
      case 'queued':
        setStatus('generating_frames');
        break;
      case 'processing':
        setStatus('encoding_video');
        break;
      case 'complete':
        setStatus('complete');
        setVideoUrl(videoProgress.videoUrl || '');
        setIsGenerating(false);
        onGenerationComplete();
        // Cleanup uploaded file after successful generation
        if (uploadedFilePath) {
          supabaseService.deleteAudioFile(uploadedFilePath).catch(console.error);
          setUploadedFilePath('');
        }
        break;
      case 'error':
        setStatus('error');
        setMessage(videoProgress.error || 'Video generation failed');
        setIsGenerating(false);
        break;
    }
  };

  const generateVideo = async () => {
    if (isGenerating) return;

    try {
      setIsGenerating(true);
      setStatus('analyzing_audio');
      setProgress(0);
      setMessage('Preparing for video generation...');
      setVideoUrl('');
      setCommandId('');
      
      onGenerationStart();

      // Create abort controller for cancellation
      abortControllerRef.current = new AbortController();

      // Step 1: Upload audio file to Supabase
      setMessage('Uploading audio file...');
      
      // Convert AudioBuffer to File
      const audioFile = await createAudioFileFromBuffer(audioData.audioBuffer, audioData.metadata.fileName);
      
      const uploadResult = await supabaseService.uploadAudioFile(audioFile, handleUploadProgress);
      setUploadedFilePath(uploadResult.filePath);

      // Step 2: Generate video using rendi.dev
      setStatus('generating_frames');
      setMessage('Starting video generation...');
      
      const videoUrl = await rendiService.generateVideo(
        {
          audioUrl: uploadResult.publicUrl,
          resolution: settings.resolution,
          fps: settings.fps,
          duration: audioData.metadata.duration,
          style: style,
          visualizationParams: visualizationParams,
        },
        handleVideoProgress
      );

      // Success handled in handleVideoProgress
    } catch (error) {
      console.error('Video generation failed:', error);
      setStatus('error');
      setMessage(error instanceof Error ? error.message : 'Video generation failed');
      setIsGenerating(false);
      
      // Cleanup uploaded file on error
      if (uploadedFilePath) {
        supabaseService.deleteAudioFile(uploadedFilePath).catch(console.error);
        setUploadedFilePath('');
      }
    }
  };

  const cancelGeneration = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    setIsGenerating(false);
    setStatus('idle');
    setProgress(0);
    setMessage('');
    
    // Cleanup uploaded file
    if (uploadedFilePath) {
      supabaseService.deleteAudioFile(uploadedFilePath).catch(console.error);
      setUploadedFilePath('');
    }
  };

  const downloadVideo = () => {
    if (videoUrl) {
      const a = document.createElement('a');
      a.href = videoUrl;
      a.download = `${audioData.metadata.fileName.replace(/\.[^/.]+$/, '')}-visualization.mp4`;
      a.target = '_blank'; // Open in new tab for rendi.dev URLs
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'analyzing_audio':
      case 'generating_frames':
      case 'encoding_video':
        return <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full" />;
      case 'complete':
        return <CheckCircle className="w-4 h-4" />;
      case 'error':
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <Zap className="w-4 h-4" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'complete':
        return 'bg-green-600 hover:bg-green-700';
      case 'error':
        return 'bg-red-600 hover:bg-red-700';
      case 'analyzing_audio':
      case 'generating_frames':
      case 'encoding_video':
        return 'bg-blue-600';
      default:
        return 'bg-purple-600 hover:bg-purple-700';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <Cloud className="w-6 h-6 text-purple-400" />
        <h3 className="text-xl font-semibold text-white">
          Cloud Video Generation
        </h3>
        <div className="px-2 py-1 bg-purple-600/20 rounded text-xs text-purple-300">
          Powered by rendi.dev
        </div>
      </div>

      {/* Generation Button */}
      <div className="space-y-4">
        <button
          onClick={isGenerating ? cancelGeneration : generateVideo}
          disabled={!audioData}
          className={`w-full flex items-center justify-center gap-3 px-6 py-4 rounded-lg font-medium transition-all duration-200 ${getStatusColor()} ${
            !audioData ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          {getStatusIcon()}
          <span>
            {isGenerating 
              ? 'Cancel Generation' 
              : status === 'complete' 
                ? 'Generate New Video' 
                : 'Generate Video'
            }
          </span>
        </button>

        {/* Progress Bar */}
        {isGenerating && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-gray-300">
              <span>{message}</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div
                className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
            {commandId && (
              <div className="text-xs text-gray-400">
                Command ID: {commandId}
              </div>
            )}
          </div>
        )}

        {/* Error Message */}
        {status === 'error' && (
          <div className="p-4 bg-red-900/20 border border-red-500/30 rounded-lg">
            <div className="flex items-center gap-2 text-red-400">
              <AlertCircle className="w-4 h-4" />
              <span className="font-medium">Generation Failed</span>
            </div>
            <p className="text-red-300 text-sm mt-1">{message}</p>
          </div>
        )}

        {/* Success State */}
        {status === 'complete' && videoUrl && (
          <div className="space-y-4">
            <div className="p-4 bg-green-900/20 border border-green-500/30 rounded-lg">
              <div className="flex items-center gap-2 text-green-400">
                <CheckCircle className="w-4 h-4" />
                <span className="font-medium">Video Generated Successfully!</span>
              </div>
              <p className="text-green-300 text-sm mt-1">
                Your video is ready for download.
              </p>
            </div>

            <button
              onClick={downloadVideo}
              className="w-full flex items-center justify-center gap-3 px-6 py-3 bg-green-600 hover:bg-green-700 rounded-lg font-medium transition-colors"
            >
              <Download className="w-4 h-4" />
              Download Video
            </button>
          </div>
        )}
      </div>

      {/* Performance Info */}
      <div className="p-4 bg-gray-800/50 rounded-lg border border-gray-700">
        <h4 className="font-medium text-white mb-2">Cloud Generation Benefits</h4>
        <ul className="text-sm text-gray-300 space-y-1">
          <li>• No browser memory limitations</li>
          <li>• Faster processing with dedicated cloud infrastructure</li>
          <li>• Support for longer videos without performance issues</li>
          <li>• High-quality encoding with professional FFmpeg</li>
        </ul>
      </div>
    </div>
  );
}

/**
 * Convert AudioBuffer to File for upload
 */
async function createAudioFileFromBuffer(audioBuffer: AudioBuffer, originalFileName: string): Promise<File> {
  // Create a simple WAV file from the AudioBuffer
  const length = audioBuffer.length;
  const sampleRate = audioBuffer.sampleRate;
  const numberOfChannels = audioBuffer.numberOfChannels;
  
  // Calculate buffer size for WAV file
  const bufferLength = 44 + length * numberOfChannels * 2; // 44 bytes header + 16-bit samples
  const arrayBuffer = new ArrayBuffer(bufferLength);
  const view = new DataView(arrayBuffer);
  
  // WAV file header
  const writeString = (offset: number, string: string) => {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  };
  
  writeString(0, 'RIFF');
  view.setUint32(4, bufferLength - 8, true);
  writeString(8, 'WAVE');
  writeString(12, 'fmt ');
  view.setUint32(16, 16, true);
  view.setUint16(20, 1, true);
  view.setUint16(22, numberOfChannels, true);
  view.setUint32(24, sampleRate, true);
  view.setUint32(28, sampleRate * numberOfChannels * 2, true);
  view.setUint16(32, numberOfChannels * 2, true);
  view.setUint16(34, 16, true);
  writeString(36, 'data');
  view.setUint32(40, length * numberOfChannels * 2, true);
  
  // Convert audio data
  let offset = 44;
  for (let i = 0; i < length; i++) {
    for (let channel = 0; channel < numberOfChannels; channel++) {
      const sample = Math.max(-1, Math.min(1, audioBuffer.getChannelData(channel)[i]));
      view.setInt16(offset, sample * 0x7FFF, true);
      offset += 2;
    }
  }
  
  const fileName = originalFileName.replace(/\.[^/.]+$/, '') + '.wav';
  return new File([arrayBuffer], fileName, { type: 'audio/wav' });
}
