export default function Loading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 flex items-center justify-center">
      <div className="text-center">
        <div className="relative">
          {/* Animated audio wave */}
          <div className="flex items-center justify-center space-x-1 mb-6">
            {[...Array(5)].map((_, i) => (
              <div
                key={i}
                className="w-2 bg-purple-500 rounded-full animate-pulse"
                style={{
                  height: `${20 + Math.sin(i) * 10}px`,
                  animationDelay: `${i * 0.1}s`,
                  animationDuration: '1s'
                }}
              />
            ))}
          </div>
          
          {/* Loading text */}
          <h2 className="text-2xl font-bold text-white mb-2">
            Audio Wave Generator
          </h2>
          <p className="text-gray-300">
            Loading audio visualization tools...
          </p>
          
          {/* Spinning loader */}
          <div className="mt-6">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto"></div>
          </div>
        </div>
      </div>
    </div>
  );
}
