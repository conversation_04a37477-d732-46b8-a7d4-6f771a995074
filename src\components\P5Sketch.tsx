'use client';

import { useEffect, useRef } from 'react';
import { WaveformStyle, VisualizationParams } from '@/types';

interface P5SketchProps {
  audioData: Uint8Array;
  style: WaveformStyle;
  width: number;
  height: number;
  isPlaying: boolean;
  visualizationParams: VisualizationParams;
}

export default function P5Sketch({ audioData, style, width, height, isPlaying, visualizationParams }: P5SketchProps) {
  const sketchRef = useRef<HTMLDivElement>(null);
  const p5InstanceRef = useRef<any>(null);
  const isPlayingRef = useRef(isPlaying);
  const audioDataRef = useRef(audioData);
  const styleRef = useRef(style);
  const visualizationParamsRef = useRef(visualizationParams);

  // Update refs when props change (without recreating p5 instance)
  useEffect(() => {
    isPlayingRef.current = isPlaying;
  }, [isPlaying]);

  useEffect(() => {
    audioDataRef.current = audioData;
  }, [audioData]);

  useEffect(() => {
    styleRef.current = style;
  }, [style]);

  useEffect(() => {
    visualizationParamsRef.current = visualizationParams;
  }, [visualizationParams]);

  // Create p5 instance only once
  useEffect(() => {
    if (!sketchRef.current || typeof window === 'undefined') return;

    // Only create if we don't have an instance
    if (p5InstanceRef.current) return;

    // Dynamically import p5.js only on client side
    const loadP5 = async () => {
      const p5 = (await import('p5')).default;

      const sketch = (p: any) => {
      let time = 0;

      p.setup = () => {
        p.createCanvas(width, height);
        p.colorMode(p.HSB, 360, 100, 100, 100);
      };

      p.draw = () => {
        // CLEAR BACKGROUND COMPLETELY to prevent traces
        p.background(0, 0, 0); // Solid black background

        // Calculate energy-based time progression
        const currentData = audioDataRef.current;
        let energyFactor = 1;

        if (currentData && currentData.length > 0) {
          const totalEnergy = Array.from(currentData).reduce((sum, val) => sum + val, 0) / currentData.length;
          energyFactor = 1 + (totalEnergy / 255) * 2; // Speed up animation based on energy
        }

        // Always update time for smooth animation, but slower when paused
        if (isPlayingRef.current) {
          time += 0.025 * energyFactor; // Dynamic speed based on energy
        } else {
          time += 0.008; // Slower animation when paused
        }

        // Add subtle background gradient only for certain styles
        const currentStyle = styleRef.current;
        if (currentStyle === 'neon_effects' || currentStyle === 'energy_burst') {
          for (let i = 0; i <= height; i += 4) {
            const alpha = p.map(i, 0, height, 3, 0);
            p.stroke(280, 20, 5, alpha);
            p.line(0, i, width, i);
          }
        }

        // Draw waveform based on style using refs
        drawWaveform(p, audioDataRef.current, styleRef.current, time);
      };

      const drawWaveform = (p: any, data: Uint8Array, style: WaveformStyle, time: number) => {
        const centerX = width / 2;
        const centerY = height / 2;

        switch (style) {
          case 'flowing_waves':
            drawFlowingWaves(p, data, centerX, centerY, time);
            break;
          case 'bar_equalizer':
            drawBarEqualizer(p, data, centerX, centerY, time);
            break;
          case 'geometric_patterns':
            drawGeometricPatterns(p, data, centerX, centerY, time);
            break;
          case 'particle_system':
            drawParticleSystem(p, data, centerX, centerY, time);
            break;
          case 'neon_effects':
            drawNeonEffects(p, data, centerX, centerY, time);
            break;
          case 'minimal_design':
            drawMinimalDesign(p, data, centerX, centerY);
            break;
          case 'energy_burst':
            drawEnergyBurst(p, data, centerX, centerY, time);
            break;
          case 'circular_spectrum':
            drawCircularSpectrum(p, data, centerX, centerY, time);
            break;
          default:
            drawFlowingWaves(p, data, centerX, centerY, time);
        }
      };

      const drawFlowingWaves = (p: any, data: Uint8Array, centerX: number, centerY: number, time: number) => {
        // Check if data has any meaningful values
        const maxValue = Math.max(...Array.from(data));
        const hasData = maxValue > 0;

        // Calculate overall energy for dynamic effects
        const totalEnergy = Array.from(data).reduce((sum, val) => sum + val, 0) / data.length;
        const energyMultiplier = hasData ? (totalEnergy / 128) * 2 + 0.5 : 1;

        // Create multiple wave layers for richness
        const numLayers = 5;
        const colors = [
          [280, 90, 95], // Bright purple
          [320, 80, 85], // Pink-purple
          [200, 70, 80], // Blue
          [260, 60, 75], // Light purple
          [340, 50, 70]  // Pink
        ];

        for (let layer = 0; layer < numLayers; layer++) {
          const [hue, sat, bright] = colors[layer];
          const alpha = 80 - layer * 15; // Fade out each layer

          p.stroke(hue, sat, bright, alpha);
          p.strokeWeight(4 - layer * 0.5);
          p.noFill();

          p.beginShape();
          for (let i = 0; i < data.length; i++) {
            let amplitude = (data[i] / 255) * (height / 2.5) * energyMultiplier;

            // If no real data, create energetic demo waves
            if (!hasData) {
              const baseWave = p.sin(i * 0.02 + time * (2 + layer * 0.3)) * (height / 4);
              const energyWave = p.sin(time * (3 + layer * 0.5)) * 0.8 + 0.2;
              const detailWave = p.sin(i * 0.1 + time * (4 + layer)) * 0.3;
              amplitude = baseWave * energyWave * (1 + detailWave);
            }

            const x = p.map(i, 0, data.length - 1, 0, width);

            // Add multiple frequency components for more organic movement
            const wave1 = p.sin(i * 0.008 + time * (2.5 + layer * 0.2)) * amplitude * 0.6;
            const wave2 = p.sin(i * 0.015 + time * (1.8 + layer * 0.3)) * amplitude * 0.3;
            const wave3 = p.sin(i * 0.025 + time * (3.2 + layer * 0.1)) * amplitude * 0.1;

            const y = centerY + wave1 + wave2 + wave3;
            p.vertex(x, y);
          }
          p.endShape();

          // Add mirrored wave for symmetry
          p.beginShape();
          for (let i = 0; i < data.length; i++) {
            let amplitude = (data[i] / 255) * (height / 3) * energyMultiplier;

            if (!hasData) {
              const baseWave = p.sin(i * 0.02 + time * (2 + layer * 0.3)) * (height / 5);
              const energyWave = p.sin(time * (3 + layer * 0.5)) * 0.8 + 0.2;
              amplitude = baseWave * energyWave;
            }

            const x = p.map(i, 0, data.length - 1, 0, width);

            const wave1 = p.sin(i * 0.008 + time * (2.5 + layer * 0.2)) * amplitude * 0.6;
            const wave2 = p.sin(i * 0.015 + time * (1.8 + layer * 0.3)) * amplitude * 0.3;

            const y = centerY - (wave1 + wave2) * 0.7; // Slightly smaller mirror
            p.vertex(x, y);
          }
          p.endShape();
        }

        // Add energy burst effects for high-energy moments
        if (hasData && totalEnergy > 180) {
          for (let i = 0; i < 20; i++) {
            const x = p.random(width);
            const y = centerY + p.random(-height/3, height/3);
            const size = p.random(2, 8) * (totalEnergy / 255);

            p.fill(p.random(260, 320), 80, 90, 60);
            p.noStroke();
            p.circle(x, y, size);
          }
        }
      };

      const drawBarEqualizer = (p: any, data: Uint8Array, centerX: number, centerY: number, time: number) => {
        const barWidth = width / data.length;
        
        for (let i = 0; i < data.length; i++) {
          const barHeight = (data[i] / 255) * height * 0.8;
          const x = i * barWidth;
          const y = height - barHeight;
          
          const hue = (i * 3 + time * 50) % 360;
          p.fill(hue, 70, 90);
          p.noStroke();
          
          p.rect(x, y, barWidth - 2, barHeight);
          
          // Add glow effect
          p.fill(hue, 50, 60, 30);
          p.rect(x - 2, y - 2, barWidth + 2, barHeight + 4);
        }
      };

      const drawGeometricPatterns = (p: any, data: Uint8Array, centerX: number, centerY: number, time: number) => {
        // Ensure clean background for geometric patterns
        p.background(0, 0, 0);
        p.translate(centerX, centerY);
        
        for (let i = 0; i < data.length; i += 4) {
          const amplitude = data[i] / 255;
          if (amplitude > 0.1) {
            const angle = p.map(i, 0, data.length, 0, p.TWO_PI);
            const radius = amplitude * 150;
            const size = amplitude * 30;
            
            const x = p.cos(angle + time) * radius;
            const y = p.sin(angle + time) * radius;
            
            p.push();
            p.translate(x, y);
            p.rotate(time + i * 0.1);
            
            const hue = (i * 5 + time * 30) % 360;
            p.fill(hue, 80, 90);
            p.noStroke();
            
            p.rect(-size/2, -size/2, size, size);
            p.pop();
          }
        }
      };

      const drawParticleSystem = (p: any, data: Uint8Array, centerX: number, centerY: number, time: number) => {
        // Ensure clean background for particle system
        p.background(0, 0, 0);
        p.translate(centerX, centerY);

        // Check if data has any meaningful values
        const maxValue = Math.max(...Array.from(data));
        const hasData = maxValue > 0;
        const totalEnergy = Array.from(data).reduce((sum, val) => sum + val, 0) / data.length;

        // Create multiple particle rings
        const numRings = 3;

        for (let ring = 0; ring < numRings; ring++) {
          const ringRadius = 80 + ring * 60;
          const particleCount = hasData ? data.length : 64;

          for (let i = 0; i < particleCount; i += 2) {
            const amplitude = hasData ? data[i] / 255 : p.sin(i * 0.1 + time) * 0.5 + 0.5;

            if (amplitude > 0.03) {
              const angle = p.map(i, 0, particleCount, 0, p.TWO_PI * (2 + ring));
              const energyPulse = hasData ? (totalEnergy / 255) : p.sin(time * 2) * 0.5 + 0.5;
              const radius = (amplitude * ringRadius + p.sin(time * (2 + ring * 0.5) + i * 0.1) * 30) * (1 + energyPulse);
              const size = amplitude * (20 + ring * 5) * (1 + energyPulse * 0.5);

              const rotationSpeed = 0.3 + ring * 0.2 + amplitude * 0.5;
              const x = p.cos(angle + time * rotationSpeed) * radius;
              const y = p.sin(angle + time * rotationSpeed) * radius;

              // Dynamic color based on position and energy
              const hue = (i * 3 + time * 80 + ring * 60) % 360;
              const saturation = 60 + amplitude * 40;
              const brightness = 70 + amplitude * 30;
              const alpha = amplitude * 80 + 20;

              p.fill(hue, saturation, brightness, alpha);
              p.noStroke();

              // Main particle
              p.circle(x, y, size);

              // Add glow effect
              p.fill(hue, saturation * 0.5, brightness, alpha * 0.3);
              p.circle(x, y, size * 2);

              // Add trails with multiple segments
              for (let trail = 1; trail <= 3; trail++) {
                const trailAngle = angle + time * rotationSpeed - trail * 0.1;
                const trailRadius = radius * (1 - trail * 0.05);
                const trailX = p.cos(trailAngle) * trailRadius;
                const trailY = p.sin(trailAngle) * trailRadius;
                const trailSize = size * (1 - trail * 0.2);
                const trailAlpha = alpha * (1 - trail * 0.3);

                p.fill(hue, saturation, brightness, trailAlpha);
                p.circle(trailX, trailY, trailSize);
              }

              // Add connecting lines between particles for high energy
              if (hasData && totalEnergy > 150 && i % 8 === 0) {
                const nextI = (i + 8) % particleCount;
                const nextAmplitude = data[nextI] / 255;
                if (nextAmplitude > 0.03) {
                  const nextAngle = p.map(nextI, 0, particleCount, 0, p.TWO_PI * (2 + ring));
                  const nextRadius = (nextAmplitude * ringRadius + p.sin(time * (2 + ring * 0.5) + nextI * 0.1) * 30) * (1 + energyPulse);
                  const nextX = p.cos(nextAngle + time * rotationSpeed) * nextRadius;
                  const nextY = p.sin(nextAngle + time * rotationSpeed) * nextRadius;

                  p.stroke(hue, saturation, brightness, 30);
                  p.strokeWeight(1);
                  p.line(x, y, nextX, nextY);
                }
              }
            }
          }
        }
      };

      const drawNeonEffects = (p: any, data: Uint8Array, centerX: number, centerY: number, time: number) => {
        // Check if data has any meaningful values
        const maxValue = Math.max(...Array.from(data));
        const hasData = maxValue > 0;
        const totalEnergy = Array.from(data).reduce((sum, val) => sum + val, 0) / data.length;

        // Dynamic glow intensity based on energy
        const glowIntensity = hasData ? 20 + (totalEnergy / 255) * 40 : 30;

        // Multiple neon layers with different colors
        const neonLayers = [
          { color: '#ff0080', blur: glowIntensity * 1.5, weight: 6, hue: 320 },
          { color: '#8b5cf6', blur: glowIntensity, weight: 4, hue: 280 },
          { color: '#00ffff', blur: glowIntensity * 0.7, weight: 3, hue: 180 },
          { color: '#ffffff', blur: glowIntensity * 0.3, weight: 1, hue: 0 }
        ];

        neonLayers.forEach((layer, layerIndex) => {
          // Set glow effect
          p.drawingContext.shadowBlur = layer.blur;
          p.drawingContext.shadowColor = layer.color;

          p.stroke(layer.hue, layerIndex === 3 ? 0 : 80, 100);
          p.strokeWeight(layer.weight);
          p.noFill();

          // Main wave
          p.beginShape();
          for (let i = 0; i < data.length; i++) {
            let amplitude = (data[i] / 255) * (height / 2.5);

            if (!hasData) {
              amplitude = (height / 4) * p.sin(i * 0.03 + time * 2) * (0.7 + 0.3 * p.sin(time * 0.8));
            }

            const x = p.map(i, 0, data.length - 1, 0, width);
            const wave1 = p.sin(i * 0.02 + time * (3 + layerIndex * 0.2)) * amplitude;
            const wave2 = p.sin(i * 0.008 + time * (2 - layerIndex * 0.1)) * amplitude * 0.3;
            const y = centerY + wave1 + wave2;
            p.vertex(x, y);
          }
          p.endShape();

          // Mirror wave
          p.beginShape();
          for (let i = 0; i < data.length; i++) {
            let amplitude = (data[i] / 255) * (height / 3);

            if (!hasData) {
              amplitude = (height / 5) * p.sin(i * 0.03 + time * 2) * (0.7 + 0.3 * p.sin(time * 0.8));
            }

            const x = p.map(i, 0, data.length - 1, 0, width);
            const wave1 = p.sin(i * 0.02 + time * (3 + layerIndex * 0.2)) * amplitude;
            const y = centerY - wave1 * 0.8;
            p.vertex(x, y);
          }
          p.endShape();
        });

        // Add electric sparks for high energy moments
        if (hasData && totalEnergy > 200) {
          p.drawingContext.shadowBlur = 15;
          p.drawingContext.shadowColor = '#ffffff';

          for (let i = 0; i < 10; i++) {
            const x = p.random(width);
            const y = centerY + p.random(-height/4, height/4);
            const sparkSize = p.random(1, 4);

            p.stroke(p.random(280, 320), 90, 100);
            p.strokeWeight(sparkSize);
            p.point(x, y);

            // Add small spark lines
            const angle = p.random(p.TWO_PI);
            const length = p.random(5, 15);
            p.line(x, y, x + p.cos(angle) * length, y + p.sin(angle) * length);
          }
        }

        // Reset shadow
        p.drawingContext.shadowBlur = 0;
      };

      const drawEnergyBurst = (p: any, data: Uint8Array, centerX: number, centerY: number, time: number) => {
        // Check if data has any meaningful values
        const maxValue = Math.max(...Array.from(data));
        const hasData = maxValue > 0;
        const totalEnergy = Array.from(data).reduce((sum, val) => sum + val, 0) / data.length;
        const energyLevel = hasData ? totalEnergy / 255 : p.sin(time * 2) * 0.5 + 0.5;

        // Dynamic background pulses
        if (energyLevel > 0.3) {
          const pulseAlpha = (energyLevel - 0.3) * 30;
          p.fill(280, 60, 20, pulseAlpha);
          p.noStroke();
          p.circle(centerX, centerY, width * energyLevel);
        }

        // Central energy core
        const coreSize = 20 + energyLevel * 100;
        p.fill(320, 90, 100, 80);
        p.noStroke();
        p.circle(centerX, centerY, coreSize);

        // Core glow
        p.fill(320, 60, 80, 40);
        p.circle(centerX, centerY, coreSize * 2);

        // Energy rays shooting outward
        const numRays = hasData ? Math.min(data.length, 32) : 16;
        for (let i = 0; i < numRays; i++) {
          const amplitude = hasData ? data[i * Math.floor(data.length / numRays)] / 255 : p.sin(i * 0.5 + time) * 0.5 + 0.5;

          if (amplitude > 0.1) {
            const angle = (i / numRays) * p.TWO_PI + time * 0.5;
            const rayLength = amplitude * (width / 3) + p.sin(time * 3 + i) * 20;
            const rayWidth = amplitude * 8 + 2;

            const endX = centerX + p.cos(angle) * rayLength;
            const endY = centerY + p.sin(angle) * rayLength;

            // Ray gradient effect
            const hue = (i * 15 + time * 50) % 360;
            p.stroke(hue, 80, 90, amplitude * 100);
            p.strokeWeight(rayWidth);
            p.line(centerX, centerY, endX, endY);

            // Ray glow
            p.stroke(hue, 60, 70, amplitude * 40);
            p.strokeWeight(rayWidth * 2);
            p.line(centerX, centerY, endX, endY);

            // Energy particles at ray ends
            if (amplitude > 0.5) {
              p.fill(hue, 90, 100, 80);
              p.noStroke();
              p.circle(endX, endY, amplitude * 15);

              // Particle explosion effect
              for (let j = 0; j < 5; j++) {
                const particleAngle = angle + p.random(-0.5, 0.5);
                const particleDistance = rayLength + p.random(10, 30);
                const particleX = centerX + p.cos(particleAngle) * particleDistance;
                const particleY = centerY + p.sin(particleAngle) * particleDistance;
                const particleSize = p.random(2, 8) * amplitude;

                p.fill(hue, 70, 90, amplitude * 60);
                p.circle(particleX, particleY, particleSize);
              }
            }
          }
        }

        // Frequency rings
        const numRings = 4;
        for (let ring = 0; ring < numRings; ring++) {
          const ringRadius = 50 + ring * 40 + energyLevel * 30;
          const ringThickness = 2 + energyLevel * 3;

          p.noFill();
          p.stroke(280 + ring * 20, 70, 80, 60 - ring * 10);
          p.strokeWeight(ringThickness);

          // Animated ring segments
          const segments = 8;
          for (let seg = 0; seg < segments; seg++) {
            const startAngle = (seg / segments) * p.TWO_PI + time * (1 + ring * 0.2);
            const endAngle = startAngle + (p.PI / segments) * (0.5 + energyLevel);

            p.arc(centerX, centerY, ringRadius * 2, ringRadius * 2, startAngle, endAngle);
          }
        }

        // Bass frequency visualization (low frequencies create ground shakes)
        if (hasData && data.length > 10) {
          const bassEnergy = Array.from(data.slice(0, 10)).reduce((sum, val) => sum + val, 0) / 10 / 255;
          if (bassEnergy > 0.3) {
            const shakeIntensity = (bassEnergy - 0.3) * 20;

            // Screen shake effect simulation with multiple lines
            for (let i = 0; i < 5; i++) {
              const shakeX = p.random(-shakeIntensity, shakeIntensity);
              const shakeY = p.random(-shakeIntensity, shakeIntensity);

              p.stroke(40, 90, 90, 30);
              p.strokeWeight(3);
              p.line(0, centerY + shakeY, width, centerY + shakeY);
              p.line(centerX + shakeX, 0, centerX + shakeX, height);
            }
          }
        }
      };

      const drawCircularSpectrum = (p: any, data: Uint8Array, centerX: number, centerY: number, time: number) => {
        // Check if data has any meaningful values
        const maxValue = Math.max(...Array.from(data));
        const hasData = maxValue > 0;
        const totalEnergy = Array.from(data).reduce((sum, val) => sum + val, 0) / data.length;

        // Circle parameters
        const baseRadius = 80;
        const maxBarLength = 120;
        const numBars = hasData ? Math.min(data.length, 128) : 64; // Limit for performance

        // Draw frequency bars in a circle
        for (let i = 0; i < numBars; i++) {
          // Calculate angle with rotation
          const angle = (i / numBars) * p.TWO_PI + time * 0.2;
          let amplitude = hasData ? data[Math.floor(i * data.length / numBars)] / 255 :
                         p.sin(i * 0.1 + time * 2) * 0.5 + 0.5;

          // Smooth amplitude changes to prevent flickering
          amplitude = p.constrain(amplitude, 0, 1);

          const barLength = amplitude * maxBarLength * visualizationParamsRef.current.sensitivity;
          const barWidth = Math.max(2, (p.TWO_PI / numBars) * baseRadius * 0.6); // Bar thickness

          // Calculate bar position relative to center
          const startX = centerX + p.cos(angle) * baseRadius;
          const startY = centerY + p.sin(angle) * baseRadius;
          const endX = centerX + p.cos(angle) * (baseRadius + barLength);
          const endY = centerY + p.sin(angle) * (baseRadius + barLength);

          // Color based on frequency position and amplitude
          const hue = (i * 360 / numBars + time * 30) % 360;
          const saturation = 70 + amplitude * 30;
          const brightness = 60 + amplitude * 40;
          const alpha = 80 + amplitude * 20;

          // Draw main bar
          p.stroke(hue, saturation, brightness, alpha);
          p.strokeWeight(barWidth);
          p.line(startX, startY, endX, endY);

          // Add glow effect for higher amplitudes
          if (amplitude > 0.3) {
            p.stroke(hue, saturation * 0.6, brightness, alpha * 0.4);
            p.strokeWeight(barWidth * 1.5);
            p.line(startX, startY, endX, endY);
          }

          // Add inner glow
          if (amplitude > 0.6) {
            p.stroke(hue, 40, 90, alpha * 0.6);
            p.strokeWeight(barWidth * 2);
            p.line(startX, startY, endX, endY);
          }

          // Add particle effects at bar tips for high energy
          if (amplitude > 0.7) {
            p.fill(hue, saturation, brightness, amplitude * 100);
            p.noStroke();
            const particleSize = amplitude * 8;
            p.circle(endX, endY, particleSize);

            // Add sparkle effect
            p.fill(0, 0, 100, amplitude * 80);
            p.circle(endX, endY, particleSize * 0.5);
          }
        }

        // Draw center circle
        p.noStroke();
        const centerSize = 20 + (totalEnergy / 255) * 30;

        // Center circle with gradient effect
        for (let r = centerSize; r > 0; r -= 2) {
          const alpha = p.map(r, 0, centerSize, 100, 0);
          const hue = (time * 50) % 360;
          p.fill(hue, 60, 90, alpha);
          p.circle(centerX, centerY, r);
        }

        // Add pulsing ring around center
        const pulseRadius = centerSize + p.sin(time * 4) * 10;
        p.noFill();
        p.stroke(280, 80, 90, 60);
        p.strokeWeight(2);
        p.circle(centerX, centerY, pulseRadius);
      };

      const drawMinimalDesign = (p: any, data: Uint8Array, centerX: number, centerY: number) => {
        p.stroke(0, 0, 100);
        p.strokeWeight(1);
        p.noFill();

        p.beginShape();
        for (let i = 0; i < data.length; i++) {
          const amplitude = (data[i] / 255) * (height / 4);
          const x = p.map(i, 0, data.length - 1, 0, width);
          const y = centerY + amplitude;
          p.vertex(x, y);
        }
        p.endShape();

        // Mirror wave
        p.beginShape();
        for (let i = 0; i < data.length; i++) {
          const amplitude = (data[i] / 255) * (height / 4);
          const x = p.map(i, 0, data.length - 1, 0, width);
          const y = centerY - amplitude;
          p.vertex(x, y);
        }
        p.endShape();
      };
    };

      // Create p5 instance
      if (sketchRef.current) {
        p5InstanceRef.current = new p5(sketch, sketchRef.current);
      }
    };

    loadP5();

    return () => {
      if (p5InstanceRef.current) {
        try {
          p5InstanceRef.current.remove();
        } catch (error) {
          console.warn('Error removing p5 instance:', error);
        }
        p5InstanceRef.current = null;
      }
    };
  }, [width, height]); // Only recreate when canvas size changes

  // Additional cleanup on component unmount
  useEffect(() => {
    return () => {
      if (p5InstanceRef.current) {
        try {
          p5InstanceRef.current.remove();
        } catch (error) {
          console.warn('Error removing p5 instance on unmount:', error);
        }
        p5InstanceRef.current = null;
      }
    };
  }, []);

  return <div ref={sketchRef} className="w-full h-full" />;
}
