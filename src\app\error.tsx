'use client';

import { useEffect } from 'react';
import { AlertCircle, RefreshCw, Home } from 'lucide-react';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Application error:', error);
  }, [error]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 flex items-center justify-center px-6">
      <div className="max-w-md w-full text-center">
        <div className="bg-black/30 backdrop-blur-sm rounded-lg p-8 border border-gray-700">
          {/* Error icon */}
          <div className="flex justify-center mb-6">
            <div className="p-3 bg-red-500/20 rounded-full">
              <AlertCircle className="w-8 h-8 text-red-400" />
            </div>
          </div>
          
          {/* Error message */}
          <h2 className="text-2xl font-bold text-white mb-4">
            Oops! Something went wrong
          </h2>
          
          <p className="text-gray-300 mb-6">
            We encountered an unexpected error while processing your audio.
            Don&apos;t worry, this happens sometimes with complex audio visualizations.
          </p>
          
          {/* Error details (only in development) */}
          {process.env.NODE_ENV === 'development' && (
            <div className="bg-gray-900/50 rounded p-4 mb-6 text-left">
              <p className="text-xs text-gray-400 font-mono break-all">
                {error.message}
              </p>
            </div>
          )}
          
          {/* Action buttons */}
          <div className="space-y-3">
            <button
              onClick={reset}
              className="w-full py-3 px-6 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Try Again</span>
            </button>
            
            <button
              onClick={() => window.location.href = '/'}
              className="w-full py-3 px-6 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
            >
              <Home className="w-4 h-4" />
              <span>Go Home</span>
            </button>
          </div>
          
          {/* Help text */}
          <div className="mt-6 pt-6 border-t border-gray-700">
            <p className="text-sm text-gray-400">
              If this problem persists, try refreshing the page or using a different audio file.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
