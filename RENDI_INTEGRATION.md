# Rendi.dev Video Generation Integration

This document describes the new cloud-based video generation system using rendi.dev's FFmpeg-as-a-Service.

## Overview

The new system replaces browser-based video generation with a powerful cloud-based solution that offers:

- **Performance**: 2-3 minute generation time for 3-minute videos
- **Reliability**: No browser memory limitations or crashes
- **Quality**: Professional FFmpeg encoding with optimized settings
- **Scalability**: Dedicated cloud infrastructure with 8 vCPUs

## Architecture

### Components

1. **RendiService** (`src/services/rendiService.ts`)
   - Handles rendi.dev API interactions
   - Manages command submission and status polling
   - Provides client-side and server-side methods

2. **SupabaseService** (`src/services/supabaseService.ts`)
   - Manages audio file uploads to Supabase storage
   - Provides public URLs for rendi.dev processing
   - Handles file cleanup and validation

3. **VideoGeneratorRendi** (`src/components/VideoGeneratorRendi.tsx`)
   - New React component for cloud video generation
   - Real-time progress tracking and status updates
   - Error handling and user feedback

4. **API Routes** (`src/app/api/rendi/`)
   - `/api/rendi/submit` - Submit video generation jobs
   - `/api/rendi/status/[commandId]` - Check job status
   - Server-side API key protection

### Data Flow

1. **Audio Upload**: User uploads audio → Supabase storage → Public URL
2. **Job Submission**: Audio URL + settings → rendi.dev API → Command ID
3. **Processing**: rendi.dev processes video with FFmpeg
4. **Polling**: Client polls status until completion
5. **Download**: User downloads video from rendi.dev CDN

## Configuration

### Environment Variables

```env
# Rendi.dev Configuration
RENDI_API_KEY=your_rendi_api_key_here

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
NEXT_PUBLIC_SUPABASE_BUCKET=temp-video-files
```

### Supabase Setup

1. Create a storage bucket named `temp-video-files`
2. Set bucket policies for public access
3. Configure MIME type restrictions for audio files
4. Set size limit to 100MB

## Performance Optimizations

### FFmpeg Settings

- **Preset**: `fast` for faster encoding
- **CRF**: `20` for good quality/speed balance
- **Audio**: `128k` bitrate for efficiency
- **Format**: `yuv420p` for compatibility
- **Flags**: `+faststart` for web optimization

### Processing Optimizations

- **vCPUs**: 8 cores for parallel processing
- **Timeout**: 10 minutes maximum
- **Polling**: Adaptive intervals (2-5 seconds)
- **Cleanup**: Automatic file deletion after processing

## Supported Visualization Styles

1. **flowing_waves** - Smooth waveform lines
2. **neon_effects** - Bright neon-style waves
3. **bar_equalizer** - Frequency bars with logarithmic scale
4. **circular_spectrum** - Linear frequency visualization
5. **minimal_design** - Clean white waveform
6. **layered_waves** - Multi-colored wave layers
7. **energy_burst** - Dynamic frequency bars
8. **geometric_patterns** - Linear frequency patterns
9. **particle_system** - Point-based visualization
10. **3d_visualization** - 3D-style frequency bars

## Error Handling

### Common Issues

1. **API Key Missing**: Check `RENDI_API_KEY` environment variable
2. **Supabase Upload Failed**: Verify bucket permissions and file size
3. **Job Timeout**: Increase `max_command_run_seconds` for long videos
4. **Invalid Audio Format**: Ensure supported audio formats (MP3, WAV, M4A, OGG)

### Error Messages

- Clear, user-friendly error messages
- Detailed logging for debugging
- Automatic cleanup on failures
- Retry mechanisms for transient errors

## Testing

### Test Page

Visit `/test-rendi-integration` to verify:

- Rendi.dev API connectivity
- Supabase storage access
- Environment variable configuration
- Job submission and status checking

### Manual Testing

1. Upload a short audio file (< 30 seconds)
2. Select a visualization style
3. Generate video using cloud method
4. Verify progress tracking and completion
5. Download and verify video quality

## Migration from Legacy System

### Feature Toggle

The application includes both systems:

- **Cloud (Fast)**: New rendi.dev system (default)
- **Browser (Legacy)**: Original browser-based system

### Gradual Migration

1. Deploy with cloud system as default
2. Monitor performance and error rates
3. Collect user feedback
4. Remove legacy system when confident

## Performance Targets

### Current Metrics

- **3-minute video**: 2-3 minutes generation time
- **Success rate**: >95% for valid audio files
- **File size**: Optimized for web delivery
- **Quality**: Professional-grade output

### Monitoring

- Track generation times by video length
- Monitor error rates and types
- Measure user satisfaction
- Analyze cost per generation

## Future Enhancements

### Planned Features

1. **Real-time Progress**: WebSocket-based progress updates
2. **Batch Processing**: Multiple videos in parallel
3. **Custom Styles**: User-defined visualization parameters
4. **Preview Generation**: Quick low-quality previews
5. **Advanced Analytics**: Detailed performance metrics

### Optimization Opportunities

1. **Caching**: Cache frequently used audio files
2. **Preprocessing**: Optimize audio before upload
3. **Compression**: Better video compression settings
4. **CDN**: Faster video delivery
5. **Scaling**: Auto-scaling based on demand

## Support and Troubleshooting

### Debug Information

- Command IDs for tracking jobs
- Detailed error messages
- Environment variable status
- API response logging

### Contact

For issues with the rendi.dev integration:

1. Check the test page for configuration issues
2. Review error messages and logs
3. Verify environment variables
4. Contact rendi.dev support if needed

## Security Considerations

### API Key Protection

- Server-side only API key usage
- No client-side exposure
- Secure environment variable storage

### File Handling

- Temporary file storage in Supabase
- Automatic cleanup after processing
- File type and size validation
- Public URL expiration

### Data Privacy

- No permanent storage of user audio
- Automatic file deletion
- Secure transmission protocols
- GDPR compliance considerations
