# 🎵 Audio Wave Generator - Professional Waveform Video Creator

Una potente applicazione Next.js che trasforma i file audio in video di visualizzazione waveform personalizzabili con sincronizzazione perfetta tra preview e video finale.

## 🏗️ Architettura del Sistema

### **Panoramica**
Audio Wave Generator utilizza un'architettura modulare che garantisce la sincronizzazione perfetta tra la preview in tempo reale e il video generato. Il sistema è progettato per mantenere coerenza visiva e parametrica attraverso tutti i componenti.

### **Componenti Principali**

#### 1. **AudioUploader** (`src/components/AudioUploader.tsx`)
- **Funzione**: Gestisce l'upload e l'analisi dei file audio
- **Processo**:
  1. Validazione file (formato, dimensione, durata)
  2. Decodifica audio tramite Web Audio API
  3. Analisi frequenze con FFT (128 bins)
  4. Generazione dati per visualizzazione
- **Output**: `AudioData` con frequencyData e metadata

#### 2. **WaveformPreview** (`src/components/WaveformPreview.tsx`)
- **Funzione**: Preview interattiva in tempo reale
- **Caratteristiche**:
  - Controlli audio (play/pause, seek, volume)
  - Sincronizzazione audio-video perfetta
  - Aggiornamento parametri in tempo reale
- **Rendering**: Delega a P5Sketch per la visualizzazione

#### 3. **P5Sketch** (`src/components/P5Sketch.tsx`)
- **Funzione**: Engine di rendering per la preview
- **Tecnologia**: p5.js per animazioni fluide
- **Caratteristiche**:
  - Rendering a 60fps
  - Sistema di tempo dinamico basato sull'energia audio
  - Supporto per tutti gli stili di visualizzazione
- **Sincronizzazione**: Usa `visualizationParams` per parametri condivisi

#### 4. **VideoGenerator** (`src/components/VideoGenerator.tsx`)
- **Funzione**: Generazione video finale
- **Processo**:
  1. Replica esatta della logica di P5Sketch
  2. Generazione frame-by-frame
  3. Encoding con FFmpeg.wasm
- **Garanzia**: Stessi parametri e algoritmi di P5Sketch

## 🎬 Generazione Video: Il Cuore Critico

### **⚠️ Componente Più Delicato**
La generazione video è la parte più complessa dell'app. Richiede sincronizzazione perfetta tra:
- **Timeline audio** (tempo reale)
- **Frame video** (sequenza discreta)
- **Dati di frequenza** (campionati)
- **Animazioni** (tempo di rendering)

### **🔧 Pipeline di Generazione**

#### **Fase 1: Preparazione Dati**
```typescript
// 1. Calcolo frame totali
const totalFrames = Math.floor(audioData.metadata.duration * settings.fps);

// 2. Per ogni frame, calcolo tempo corrispondente
const timeProgress = frame / totalFrames;
const currentTime = timeProgress * audioData.metadata.duration;

// 3. Mapping ai dati di frequenza
const frameIndex = Math.floor(timeProgress * audioData.frequencyData.length);
const frequencyFrame = audioData.frequencyData[frameIndex];
```

#### **Fase 2: Sincronizzazione Temporale (CRITICA)**
```typescript
// PROBLEMA: P5Sketch usa accumulo dinamico, VideoGenerator usa calcolo diretto
// SOLUZIONE: Calcolo equivalente che produce stesso risultato

// P5Sketch (preview) - accumulo frame-by-frame
time += 0.025 * energyFactor; // Varia con l'energia audio

// VideoGenerator (video) - calcolo diretto equivalente
const animationTime = currentTime * 2.5; // Fattore medio calibrato
```

#### **Fase 3: Processamento Audio Identico**
```typescript
// CRITICO: Stesso scaling in entrambi i componenti
const audioDataArray = new Uint8Array(frequencyFrame.length);
for (let i = 0; i < frequencyFrame.length; i++) {
  // Formula IDENTICA in P5Sketch e VideoGenerator
  audioDataArray[i] = Math.min(255, Math.max(10, frequencyFrame[i] * 100));
}
```

#### **Fase 4: Rendering Frame**
```typescript
// Stessa funzione di rendering, stessi parametri
const barLength = amplitude * maxBarLength * visualizationParams.sensitivity;
```

#### **Fase 5: Encoding Video**
```typescript
// FFmpeg.wasm encoding con sincronizzazione audio
await ffmpeg.exec([
  '-framerate', settings.fps.toString(),
  '-i', 'frame_%d.png',
  '-i', 'audio.wav',
  '-c:v', 'libx264',
  '-c:a', 'aac',
  '-shortest', // CRITICO: taglia al più corto tra audio e video
  'output.mp4'
]);
```

### **🚨 Punti di Fallimento Critici**

#### **1. Desincronizzazione Temporale**
- **Sintomo**: Preview e video hanno velocità diverse
- **Causa**: Fattore di tempo sbagliato in VideoGenerator
- **Fix**: Calibrare `animationTime = currentTime * FACTOR`

#### **2. Scaling Audio Diverso**
- **Sintomo**: Ampiezze diverse tra preview e video
- **Causa**: Formule di scaling non identiche
- **Fix**: Verificare che entrambi usino `* 100` scaling

#### **3. Frame Rate Mismatch**
- **Sintomo**: Video troppo veloce/lento rispetto all'audio
- **Causa**: FPS sbagliato nel calcolo totalFrames
- **Fix**: Verificare `duration * fps = totalFrames`

#### **4. FFmpeg Encoding Issues**
- **Sintomo**: Audio e video non sincronizzati nel file finale
- **Causa**: Parametri FFmpeg sbagliati
- **Fix**: Usare `-shortest` flag e verificare framerate

## 🔄 Sistema di Sincronizzazione

### **Architettura a Due Motori**

#### **Preview Engine (P5Sketch)**
- **Tecnologia**: p5.js canvas rendering
- **Frequenza**: 60fps real-time
- **Tempo**: Accumulo dinamico basato su energia audio
- **Scopo**: Feedback immediato e interattività

#### **Video Engine (VideoGenerator)**
- **Tecnologia**: p5.js headless + FFmpeg.wasm
- **Frequenza**: Configurabile (30/60fps)
- **Tempo**: Calcolo diretto equivalente
- **Scopo**: Output finale identico alla preview

### **Garanzie di Sincronizzazione**

#### **1. Parametri Condivisi**
```typescript
interface VisualizationParams {
  sensitivity: number; // Moltiplicatore ampiezza
}
// Stesso oggetto passato a entrambi i motori
```

#### **2. Logica Unificata**
```typescript
// Stessa funzione di rendering
const drawCircularSpectrum = (p, data, centerX, centerY, time, visualizationParams) => {
  const barLength = amplitude * maxBarLength * visualizationParams.sensitivity;
  // Identica in P5Sketch.tsx e VideoGenerator.tsx
};
```

#### **3. Validazione Continua**
- Preview 3s per test rapidi
- Confronto visivo immediato
- Parametri modificabili in real-time

## ⚙️ Sistema di Parametri

### **Attuale: Sensitivity**
- **Range**: 0.1x - 3.0x
- **Effetto**: Controlla la lunghezza delle barre/ampiezza delle onde
- **Implementazione**: Moltiplicatore applicato all'ampiezza
- **UI**: Slider con feedback visivo in tempo reale

### **Futuri Parametri** (Roadmap)
- **Color Intensity**: Saturazione e luminosità dei colori
- **Animation Speed**: Velocità delle animazioni indipendente dall'audio
- **Smoothing**: Livello di smoothing delle transizioni
- **Reactivity**: Sensibilità alle variazioni di frequenza
- **Background**: Opzioni per sfondi personalizzati

### **Architettura Estensibile**
```typescript
// Facile aggiungere nuovi parametri
interface VisualizationParams {
  sensitivity: number;
  colorIntensity: number;    // Nuovo parametro
  animationSpeed: number;    // Nuovo parametro
  smoothing: number;         // Nuovo parametro
}
```

## ✨ Funzionalità Principali

### 🎧 **Processamento Audio**
- **Formati**: MP3, WAV, FLAC, M4A, OGG
- **Upload**: Drag & drop con validazione real-time
- **Limiti**: 100MB, 15 minuti per generazione video
- **Analisi**: FFT a 128 bins con curve di risposta realistica

### 🎨 **Stili di Visualizzazione** (9 disponibili)
- **Flowing Waves** - Onde sinusoidali multi-layer
- **Circular Spectrum** - Spettro circolare rotante (con sensitivity)
- **Bar Equalizer** - Barre di frequenza classiche
- **Geometric Patterns** - Forme geometriche animate
- **Particle System** - Sistemi di particelle dinamici
- **Neon Effects** - Linee luminose con glow
- **Energy Burst** - Esplosioni radiali
- **Layered Waves** - Onde stratificate complesse
- **Minimal Design** - Waveform pulite

### 🎬 **Output Video**
- **Risoluzioni**: 720p, 1080p, 1440p
- **Frame Rate**: 30fps (veloce) / 60fps (fluido)
- **Encoding**: H.264 MP4 client-side con FFmpeg.wasm
- **Sincronizzazione**: Audio-video perfetta garantita
- **Test**: Preview 3s per validazione rapida

## 🚀 Come Usare

### 1. **Avvia l'Applicazione**
```bash
npm install
npm run dev
```
Apri [http://localhost:3000](http://localhost:3000)

### 2. **Carica un File Audio**
- Trascina un file audio nell'area di upload
- Aspetta che l'analisi sia completata (progress bar)
- Vedrai "Audio loaded!" quando è pronto

### 3. **Seleziona uno Stile**
- Scegli tra i 9 stili disponibili
- Ogni stile ha caratteristiche e animazioni uniche
- La preview si aggiorna immediatamente

### 4. **Configura le Impostazioni**
- **Risoluzione**: 720p, 1080p, 1440p
- **Frame Rate**: 30fps (più veloce) o 60fps (più fluido)
- **Qualità**: Medium, High, Maximum
- **Sensitivity**: 0.1x - 3.0x (lunghezza barre/onde)

### 5. **Preview Interattiva**
- Premi play per vedere la waveform animata
- Usa i controlli per navigare nell'audio
- Regola la sensibilità e vedi i cambiamenti in tempo reale
- La visualizzazione è identica al video finale

### 6. **Test con Preview 3s**
- Clicca "Generate 3s Preview" per un test rapido
- Verifica che il risultato sia quello desiderato
- Confronta con la preview principale per confermare la sincronizzazione

### 7. **Genera il Video Completo**
- Clicca "Generate Full Video"
- **Processo**:
  1. Loading FFmpeg.wasm (~10-20 secondi, solo prima volta)
  2. Generazione frame (~1-3 minuti, dipende dalla durata)
  3. Encoding video (~30 secondi - 2 minuti)
- **Download**: Il file MP4 sarà scaricato automaticamente

## 🛠️ Stack Tecnologico e Responsabilità

### **Core Framework**
- **Next.js 15**: App router, SSR, ottimizzazioni bundle
- **React 18**: Componenti, hooks, gestione stato
- **TypeScript**: Type safety, intellisense, error prevention

### **Audio Processing**
- **Web Audio API**:
  - Decodifica file audio (MP3, WAV, FLAC, etc.)
  - Creazione AudioContext e AudioBuffer
  - Analisi frequenze real-time con AnalyserNode
- **Custom FFT Analysis**:
  - Divisione in 128 bins di frequenza
  - Calcolo RMS per ampiezza
  - Applicazione curve di risposta realistica

### **Rendering Engines**
- **p5.js**:
  - Preview real-time a 60fps
  - Rendering headless per video generation
  - Gestione canvas, colori HSB, forme geometriche
  - Sistema di coordinate e trasformazioni
- **HTML5 Canvas**:
  - Backup rendering per compatibilità
  - Export frame come data URLs
  - Gestione pixel-level per encoding

### **Video Processing**
- **FFmpeg.wasm**:
  - Encoding H.264 MP4 client-side
  - Sincronizzazione audio-video
  - Compressione e ottimizzazione
  - Supporto per diversi codec e formati
- **Blob API**:
  - Gestione file binari in memoria
  - Conversione AudioBuffer → WAV
  - Download automatico file generati

### **UI/UX**
- **Tailwind CSS**: Styling responsive, tema dark, animazioni
- **Lucide React**: Icone consistenti e scalabili
- **Custom Components**: Slider, progress bar, file upload

### **Performance & Memory**
- **Web Workers** (futuro): Processing pesante off-main-thread
- **Streaming Processing**: Yield control per evitare blocking
- **Memory Management**: Cleanup automatico di canvas e blob

## 📋 Requisiti

- **Browser**: Chrome 80+, Firefox 75+, Safari 14+, Edge 80+
- **RAM**: Minimo 4GB per file audio grandi
- **Connessione**: Stabile per download FFmpeg.wasm
- **Hardware**: Accelerazione hardware consigliata

## ⚡ Performance

### **Limiti Consigliati**
- **Durata Audio**: Max 15 minuti per video generation
- **Dimensione File**: Max 100MB
- **Risoluzione**: 1080p consigliata per bilanciare qualità/performance

### **Tempi di Elaborazione** (approssimativi)
- **Audio 30s @ 1080p 60fps**: ~3-5 minuti totali
- **Audio 10s @ 720p 30fps**: ~1-2 minuti totali
- **Loading FFmpeg**: ~10-20 secondi (solo prima volta)

## 🔧 Risoluzione Problemi

### **Video Download Fallisce**
- Controlla la console browser per errori FFmpeg
- Riprova con file audio più piccolo
- Usa risoluzione/fps più bassi

### **Preview Non Funziona**
- Verifica che l'audio sia stato caricato completamente
- Controlla i permessi audio del browser
- Ricarica la pagina se necessario

### **Errori di Memoria**
- Riduci la durata dell'audio (max 60s)
- Usa risoluzione più bassa (720p invece di 1080p)
- Chiudi altre schede del browser

## 🎯 Roadmap

### **Parametri di Visualizzazione** (Prossimo)
- [ ] **Color Intensity**: Controllo saturazione e luminosità
- [ ] **Animation Speed**: Velocità animazioni indipendente dall'audio
- [ ] **Smoothing**: Livello di smoothing delle transizioni
- [ ] **Reactivity**: Sensibilità alle variazioni di frequenza
- [ ] **Background Options**: Sfondi personalizzati e gradienti

### **Nuovi Stili di Visualizzazione**
- [ ] **3D Visualization**: Rendering 3D con Three.js
- [ ] **Frequency Heatmap**: Mappe di calore delle frequenze
- [ ] **Waveform Tunnel**: Tunnel di waveform in prospettiva
- [ ] **Audio Landscape**: Paesaggi generati dall'audio

### **Funzionalità Avanzate**
- [ ] **Preset Manager**: Salvataggio e condivisione preset
- [ ] **Social Media Presets**: Formati ottimizzati (Instagram, TikTok, YouTube)
- [ ] **Batch Processing**: Elaborazione multipla di file
- [ ] **Export Formats**: GIF, WebM, PNG sequence
- [ ] **Audio Effects**: Equalizer, reverb, compressor

### **Integrazione e Storage**
- [ ] **Supabase Integration**: Salvataggio progetti cloud
- [ ] **User Accounts**: Gestione utenti e progetti
- [ ] **Sharing**: Condivisione progetti e preset
- [ ] **Analytics**: Metriche di utilizzo (privacy-first)

## � Playbook di Debug e Ricostruzione

### **Diagnostica Problemi di Sincronizzazione**

#### **Test di Verifica**
```typescript
// 1. Test temporale - verificare che i tempi corrispondano
console.log('Preview time:', time);
console.log('Video time:', animationTime);
console.log('Audio time:', currentTime);

// 2. Test dati audio - verificare scaling identico
console.log('Preview data:', previewAudioData[0]);
console.log('Video data:', videoAudioData[0]);

// 3. Test parametri - verificare propagazione
console.log('Sensitivity:', visualizationParams.sensitivity);
```

#### **Ricostruzione Sistema di Sincronizzazione**

**Step 1: Verificare Pipeline Audio**
```typescript
// AudioUploader.tsx - Analisi deve produrre dati consistenti
const frequencyArray = new Float32Array(frequencyBins);
// CRITICO: Stesso numero di bins (128)
// CRITICO: Stesso scaling (frequencyFrame[i] * 100)
```

**Step 2: Verificare Tempo di Animazione**
```typescript
// P5Sketch.tsx - Accumulo dinamico
time += 0.025 * energyFactor;

// VideoGenerator.tsx - Calcolo equivalente
const animationTime = currentTime * 2.5; // Calibrare questo fattore
```

**Step 3: Verificare Rendering Identico**
```typescript
// Stessa funzione in entrambi i file
const barLength = amplitude * maxBarLength * visualizationParams.sensitivity;
// CRITICO: Stessi parametri, stessa formula
```

**Step 4: Verificare FFmpeg Encoding**
```typescript
await ffmpeg.exec([
  '-framerate', settings.fps.toString(), // CRITICO: FPS corretto
  '-i', 'frame_%d.png',
  '-i', 'audio.wav',
  '-c:v', 'libx264',
  '-shortest' // CRITICO: Sincronizzazione audio-video
]);
```

### **Checklist di Ricostruzione Completa**

#### **🔍 Fase 1: Analisi Audio**
- [ ] AudioBuffer decodificato correttamente
- [ ] 128 bins di frequenza generati
- [ ] Scaling `* 100` applicato
- [ ] Dati salvati in `audioData.frequencyData`

#### **🔍 Fase 2: Preview Engine**
- [ ] P5Sketch riceve dati corretti
- [ ] Tempo accumula con `time += 0.025 * energyFactor`
- [ ] Parametri applicati correttamente
- [ ] Rendering a 60fps fluido

#### **🔍 Fase 3: Video Engine**
- [ ] VideoGenerator riceve stessi dati
- [ ] Tempo calcolato con `currentTime * 2.5`
- [ ] Stessi parametri di P5Sketch
- [ ] Frame generati in sequenza corretta

#### **🔍 Fase 4: Encoding**
- [ ] FFmpeg.wasm caricato correttamente
- [ ] Frame rate corretto nel comando
- [ ] Audio WAV generato correttamente
- [ ] Flag `-shortest` per sincronizzazione

### **🔧 Strumenti di Debug**

#### **Console Logging**
```typescript
// Aggiungere in punti critici
console.log('Frame:', frame, 'Time:', currentTime, 'AnimTime:', animationTime);
console.log('AudioData length:', audioDataArray.length);
console.log('Sensitivity:', visualizationParams.sensitivity);
```

#### **Visual Debugging**
```typescript
// Overlay informazioni su canvas
p.fill(255);
p.text(`Time: ${time.toFixed(2)}`, 10, 20);
p.text(`Energy: ${energyFactor.toFixed(2)}`, 10, 40);
```

#### **Performance Monitoring**
```typescript
// Monitorare performance
const startTime = performance.now();
// ... rendering code ...
const endTime = performance.now();
console.log('Frame time:', endTime - startTime, 'ms');
```

## 🤝 Contribuire

### **Setup Sviluppo**
```bash
git clone <repository>
cd audio-wave-generator
npm install
npm run dev
```

### **Aggiungere Nuovi Parametri**
1. Aggiorna `VisualizationParams` in `src/types/index.ts`
2. Aggiungi controllo UI in `src/app/page.tsx`
3. Implementa logica in `P5Sketch.tsx` e `VideoGenerator.tsx`
4. **CRITICO**: Testa sincronizzazione preview-video

### **Aggiungere Nuovi Stili**
1. Aggiungi stile a `WaveformStyle` type
2. Implementa funzione di rendering in **entrambi** i componenti
3. Aggiungi alla StyleSelector
4. Testa con tutti i parametri esistenti

## 📊 Riepilogo Architetturale

### **Punti di Forza**
✅ **Sincronizzazione Perfetta**: Preview e video identici
✅ **Privacy-First**: Processing completamente client-side
✅ **Scalabilità**: Architettura estensibile per nuovi parametri
✅ **Performance**: Ottimizzato per file audio grandi
✅ **UX**: Feedback real-time e controlli intuitivi

### **Componenti Critici da Preservare**
🔒 **Sistema di Sincronizzazione**: `visualizationParams` condivisi
🔒 **Pipeline Audio**: Scaling `* 100` identico
🔒 **Gestione Tempo**: Equivalenza tra accumulo e calcolo diretto
🔒 **FFmpeg Encoding**: Flag `-shortest` per sync audio-video

### **Metriche di Successo**
- **Identità Visiva**: Preview === Video finale
- **Performance**: <5min per 30s @ 1080p 60fps
- **Compatibilità**: 95%+ browser moderni
- **Affidabilità**: 0 desincronizzazioni audio-video

---

## 🚀 Deployment & Production

### **Live Demo**
Visit [www.audiowavegenerator.com](https://www.audiowavegenerator.com)

### **Build & Deploy**
```bash
# Build for production
npm run build

# Test production build locally
npm run start
```

### **Vercel Deployment**
1. Connect GitHub repository to Vercel
2. Set custom domain: `www.audiowavegenerator.com`
3. Auto-deploy on push to main branch

### **SEO Optimization**
- ✅ Sitemap.xml generated
- ✅ Robots.txt configured
- ✅ Structured data (JSON-LD)
- ✅ Open Graph & Twitter Cards
- ✅ PWA manifest
- ✅ Google Search Console ready

---

## ❤️ Made with Love - Father & Son Project

This project was created with love as a collaboration between father and son:
- **Papà Mattia** - Lead Developer & Mentor
- **Figlio Riccardo** - Junior Developer & Creative Mind

A beautiful journey of learning, coding, and creating together. Building memories, one line of code at a time 👨‍👦‍💻

---

**🎵 Audio Wave Generator** - *Sincronizzazione perfetta tra preview interattive e generazione video*

*Sviluppato con Next.js, p5.js e FFmpeg.wasm per dimostrare l'eccellenza nell'audio-video processing client-side.*
